<template>
  <PageContainer
    title="工艺BOM管理"
    description="管理产品配方工艺和物料清单"
    :breadcrumb="breadcrumb"
    icon="List"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建BOM
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出BOM
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="BOM总数"
          :value="stats.total"
          icon="List"
          theme="primary"
          description="系统中的工艺BOM总数"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="使用中"
          :value="stats.active"
          icon="CircleCheck"
          theme="success"
          description="当前使用中的BOM"
          :clickable="true"
          @click="filterByStatus('active')"
        />
        <StatCard
          title="审核中"
          :value="stats.review"
          icon="Clock"
          theme="warning"
          description="等待审核的BOM"
          :clickable="true"
          @click="filterByStatus('review')"
        />
        <StatCard
          title="平均成本"
          :value="stats.avgCost"
          icon="Money"
          theme="info"
          description="单位产品平均成本"
          suffix="元"
          :clickable="true"
          @click="showCostAnalysis"
        />
      </div>
    </div>

    <!-- BOM类型导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>BOM类型</h3>
        <div class="category-tabs">
          <el-button
            v-for="type in bomTypes"
            :key="type.value"
            :type="selectedType === type.value ? 'primary' : 'default'"
            @click="selectType(type.value)"
          >
            {{ type.label }}
          </el-button>
          <el-button
            :type="selectedType === '' ? 'primary' : 'default'"
            @click="selectType('')"
          >
            全部类型
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in bomStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索BOM编码、产品名称"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="工艺BOM列表"
      description="查看和管理所有工艺BOM"
      :show-selection="true"
      :show-index="true"
      :search-fields="['bomCode', 'bomName', 'productInfo.productName']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- BOM类型列插槽 -->
      <template #bomType="{ row }">
        <el-tag type="info">
          {{ getBOMTypeLabel(row.bomType) }}
        </el-tag>
      </template>

      <!-- 产品信息列插槽 -->
      <template #productInfo="{ row }">
        <div class="product-info">
          <div class="product-name">{{ row.productInfo.productName }}</div>
          <div class="product-spec">{{ row.productInfo.specification }}</div>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 成本分析列插槽 -->
      <template #costAnalysis="{ row }">
        <div class="cost-info">
          <div class="total-cost">总成本: ¥{{ row.costAnalysis.totalCost.toLocaleString() }}</div>
          <div class="unit-cost">单位成本: ¥{{ row.costAnalysis.costPerUnit.toFixed(2) }}</div>
        </div>
      </template>

      <!-- 收率信息列插槽 -->
      <template #yieldInfo="{ row }">
        <div class="yield-info">
          <el-progress
            :percentage="row.yieldInfo.yieldEfficiency"
            :stroke-width="8"
            :show-text="false"
          />
          <div class="yield-text">{{ row.yieldInfo.yieldEfficiency }}%</div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status !== 'active'"
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="success"
          size="small"
          :icon="CopyDocument"
          @click="handleCopy(row)"
        >
          复制
        </el-button>
      </template>
    </DataTable>

    <!-- BOM详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="工艺BOM详情"
      width="1400px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="BOM编码">
              {{ currentRow.bomCode }}
            </el-descriptions-item>
            <el-descriptions-item label="BOM名称">
              {{ currentRow.bomName }}
            </el-descriptions-item>
            <el-descriptions-item label="版本">
              {{ currentRow.bomVersion }}
            </el-descriptions-item>
            <el-descriptions-item label="类型">
              {{ getBOMTypeLabel(currentRow.bomType) }}
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">
              {{ currentRow.productInfo.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="产品规格">
              {{ currentRow.productInfo.specification }}
            </el-descriptions-item>
            <el-descriptions-item label="批次大小">
              {{ currentRow.productInfo.batchSize }} {{ currentRow.productInfo.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="生效日期">
              {{ currentRow.effectiveDate }}
            </el-descriptions-item>
            <el-descriptions-item label="失效日期">
              {{ currentRow.expiryDate || '长期有效' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ currentRow.createdBy }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(currentRow.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 工艺步骤 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>工艺步骤</h3>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="step in currentRow.processSteps"
              :key="step.id"
              :timestamp="`步骤${step.stepNumber}`"
              placement="top"
            >
              <el-card>
                <div class="step-header">
                  <h4>{{ step.stepName }}</h4>
                  <el-tag>{{ getStepTypeLabel(step.stepType) }}</el-tag>
                </div>
                <p class="step-description">{{ step.description }}</p>
                <div class="step-details">
                  <div class="detail-row">
                    <span class="detail-label">工艺时间:</span>
                    <span class="detail-value">{{ step.duration }}分钟</span>
                  </div>
                  <div v-if="step.temperature" class="detail-row">
                    <span class="detail-label">温度:</span>
                    <span class="detail-value">{{ step.temperature }}℃</span>
                  </div>
                  <div v-if="step.pressure" class="detail-row">
                    <span class="detail-label">压力:</span>
                    <span class="detail-value">{{ step.pressure }}MPa</span>
                  </div>
                  <div v-if="step.humidity" class="detail-row">
                    <span class="detail-label">湿度:</span>
                    <span class="detail-value">{{ step.humidity }}%</span>
                  </div>
                </div>
                <div v-if="step.criticalControlPoints.length > 0" class="critical-points">
                  <h5>关键控制点:</h5>
                  <el-tag
                    v-for="point in step.criticalControlPoints"
                    :key="point"
                    type="danger"
                    size="small"
                    class="point-tag"
                  >
                    {{ point }}
                  </el-tag>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 物料清单 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>物料清单</h3>
          </template>
          <el-table :data="currentRow.materialList" border>
            <el-table-column prop="materialCode" label="物料编码" width="120" />
            <el-table-column prop="materialName" label="物料名称" min-width="150" />
            <el-table-column prop="materialType" label="类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ getMaterialTypeLabel(row.materialType) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="right" />
            <el-table-column prop="unit" label="单位" width="60" />
            <el-table-column prop="unitCost" label="单价" width="80" align="right">
              <template #default="{ row }">
                ¥{{ row.unitCost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalCost" label="总价" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.totalCost.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="supplier" label="供应商" min-width="120" />
            <el-table-column prop="leadTime" label="交期" width="60" align="center">
              <template #default="{ row }">
                {{ row.leadTime }}天
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 成本分析 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>成本分析</h3>
          </template>
          <div class="cost-analysis">
            <div class="cost-summary">
              <div class="summary-item">
                <span class="summary-label">总成本</span>
                <span class="summary-value primary">¥{{ currentRow.costAnalysis.totalCost.toLocaleString() }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">单位成本</span>
                <span class="summary-value">¥{{ currentRow.costAnalysis.costPerUnit.toFixed(2) }}</span>
              </div>
            </div>
            <div class="cost-breakdown">
              <h4>成本构成</h4>
              <div class="breakdown-grid">
                <div
                  v-for="item in currentRow.costAnalysis.costBreakdown"
                  :key="item.category"
                  class="breakdown-item"
                >
                  <div class="breakdown-header">
                    <span class="breakdown-category">{{ item.category }}</span>
                    <span class="breakdown-percentage">{{ item.percentage.toFixed(1) }}%</span>
                  </div>
                  <div class="breakdown-amount">¥{{ item.amount.toLocaleString() }}</div>
                  <div class="breakdown-description">{{ item.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
        <el-button type="success" @click="handleCopy(currentRow)">复制</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  List,
  Plus,
  Download,
  Search,
  CircleCheck,
  Clock,
  Money,
  View,
  Edit,
  CopyDocument
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getProcessBOMs,
  bomTypes,
  bomStatuses,
  stepTypes,
  materialTypes,
  type ProcessBOM
} from '../../mock/process-bom';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '生产与运营管理', path: '/production' },
  { title: '工艺BOM', path: '/production/bom' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<ProcessBOM | null>(null);
const tableRef = ref();
const selectedType = ref('');

// 表格数据
const tableData = ref<ProcessBOM[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  bomType: '',
  status: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const boms = tableData.value;
  const totalCost = boms.reduce((sum, bom) => sum + bom.costAnalysis.totalCost, 0);
  return {
    total: boms.length,
    active: boms.filter(b => b.status === 'active').length,
    review: boms.filter(b => b.status === 'review').length,
    avgCost: boms.length > 0 ? (totalCost / boms.length).toFixed(0) : '0'
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'bomCode',
    label: 'BOM编码',
    width: 140,
    fixed: 'left'
  },
  {
    prop: 'bomName',
    label: 'BOM名称',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'bomType',
    label: 'BOM类型',
    width: 100,
    slot: 'bomType'
  },
  {
    prop: 'productInfo',
    label: '产品信息',
    width: 180,
    slot: 'productInfo'
  },
  {
    prop: 'bomVersion',
    label: '版本',
    width: 80
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'costAnalysis',
    label: '成本分析',
    width: 150,
    slot: 'costAnalysis'
  },
  {
    prop: 'yieldInfo',
    label: '收率',
    width: 100,
    slot: 'yieldInfo'
  },
  {
    prop: 'effectiveDate',
    label: '生效日期',
    width: 100
  },
  {
    prop: 'createdBy',
    label: '创建人',
    width: 100
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getProcessBOMs({
      bomType: filters.bomType || selectedType.value,
      status: filters.status,
      dateRange: filters.dateRange,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.bomType = '';
  filters.status = '';
  filters.dateRange = null;
  filters.keyword = '';
  selectedType.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const selectType = (type: string) => {
  selectedType.value = type;
  filters.bomType = type;
  loadData();
};

const getBOMTypeLabel = (type: string) => {
  const item = bomTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getStatusLabel = (status: string) => {
  const item = bomStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = bomStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getStepTypeLabel = (type: string) => {
  const item = stepTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getMaterialTypeLabel = (type: string) => {
  const item = materialTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const handleAdd = () => {
  ElMessage.success('新建BOM对话框已打开');
  // 这里可以添加打开新建对话框的逻辑
};

const handleEdit = (row: ProcessBOM) => {
  ElMessage.info(`编辑BOM: ${row.bomName}`);
  detailVisible.value = false;
};

const handleView = (row: ProcessBOM) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleCopy = (row: ProcessBOM) => {
  ElMessage.info(`复制BOM: ${row.bomName}`);
};

const handleExport = () => {
  ElMessage.success('BOM数据导出已开始');
  // 这里可以添加实际的导出逻辑
};

const handleSelectionChange = (selection: ProcessBOM[]) => {
  console.log('选中的BOM:', selection);
};

const handleRowClick = (row: ProcessBOM) => {
  console.log('点击的BOM:', row);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const showCostAnalysis = () => {
  ElMessage.success('成本分析报告已生成');
  // 这里可以添加成本分析的逻辑
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input,
.filter-item .el-date-picker {
  width: 100%;
}

/* 产品信息样式 */
.product-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.product-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.product-spec {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 成本信息样式 */
.cost-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.total-cost {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.unit-cost {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 收率信息样式 */
.yield-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.yield-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  min-width: 40px;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 工艺步骤样式 */
.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.step-header h4 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.step-description {
  margin: 0 0 var(--spacing-sm) 0;
  line-height: var(--line-height-relaxed);
  color: var(--text-regular);
}

.step-details {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.detail-row {
  display: flex;
  gap: var(--spacing-xs);
}

.detail-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.detail-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.critical-points {
  margin-top: var(--spacing-sm);
}

.critical-points h5 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.point-tag {
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

/* 成本分析样式 */
.cost-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.cost-summary {
  display: flex;
  gap: var(--spacing-xxl);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-light);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-color-light);
}

.summary-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.summary-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.summary-value.primary {
  color: var(--primary-color);
}

.cost-breakdown h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.breakdown-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.breakdown-item {
  padding: var(--spacing-md);
  background: var(--bg-white);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-base);
}

.breakdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.breakdown-category {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.breakdown-percentage {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.breakdown-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.breakdown-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .step-details {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .cost-summary {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .breakdown-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.detail-card,
.breakdown-item,
.summary-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
