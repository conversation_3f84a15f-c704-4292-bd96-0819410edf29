<template>
  <PageContainer
    title="仪表盘"
    description="系统概览和快速操作"
    :breadcrumb="breadcrumb"
    icon="Dashboard"
  >
    <!-- 欢迎区域 -->
    <div class="welcome-section mb-xxl">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ authStore.userName }}！</h2>
            <p class="welcome-subtitle">
              当前角色：{{ getRoleDisplayName(authStore.userRole) }}
            </p>
            <p class="welcome-time">
              {{ getCurrentTimeGreeting() }}，今天是 {{ getCurrentDate() }}
            </p>
          </div>
          <div class="welcome-avatar">
            <el-avatar :size="80" :src="authStore.userAvatar" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <h3 class="section-title">快速操作</h3>
      <div class="quick-actions-grid">
        <el-card
          v-for="action in quickActions"
          :key="action.key"
          class="quick-action-card"
          :class="{ disabled: !hasActionPermission(action) }"
          @click="handleQuickAction(action)"
        >
          <div class="quick-action-content">
            <el-icon :size="32" class="action-icon">
              <component :is="action.icon" />
            </el-icon>
            <h4>{{ action.title }}</h4>
            <p>{{ action.description }}</p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 数据概览区域 -->
    <div class="overview-section mb-xxl">
      <h3 class="section-title mb-lg">数据概览</h3>
      <div class="overview-grid">
        <StatCard
          title="生产批次"
          :value="mockData.productionBatches"
          icon="Setting"
          theme="primary"
          :trend="12"
          trend-text="+12% 较上月"
          description="本月完成生产批次"
          :animated="true"
          :clickable="true"
          @click="handleStatClick('production')"
        />

        <StatCard
          title="质量检测"
          :value="mockData.qualityTests"
          icon="CircleCheck"
          theme="success"
          :trend="8"
          trend-text="+8% 较上月"
          description="本月质量检测次数"
          :animated="true"
          :clickable="true"
          @click="handleStatClick('quality')"
        />

        <StatCard
          title="原料库存"
          :value="mockData.materialStock"
          icon="Box"
          theme="warning"
          :trend="-3"
          trend-text="-3% 较上月"
          description="当前原料库存总量"
          suffix=" 吨"
          :animated="true"
          :clickable="true"
          @click="handleStatClick('materials')"
        />

        <StatCard
          title="活跃用户"
          :value="mockData.activeUsers"
          icon="User"
          theme="info"
          :trend="15"
          trend-text="+15% 较上月"
          description="本月活跃用户数量"
          :animated="true"
          :clickable="true"
          @click="handleStatClick('users')"
        />
      </div>
    </div>

    <!-- 最近活动区域 -->
    <div class="recent-activities-section">
      <h3 class="section-title">最近活动</h3>
      <el-card class="activities-card">
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
              <el-tag :type="activity.status" size="small">
                {{ activity.statusText }}
              </el-tag>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  Setting,
  CircleCheck,
  Box,
  User,
  ArrowUp,
  ArrowDown,
  DataAnalysis,
  Document,
  Link,
  Management,
  Monitor,
  Search
} from '@element-plus/icons-vue';
import { useAuthStore } from '../../stores/auth';
import { ROLE_LABELS } from '../../types';
import PageContainer from '../../components/common/PageContainer.vue';
import StatCard from '../../components/common/StatCard.vue';

// 路由和状态管理
const router = useRouter();
const authStore = useAuthStore();

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' }
]);

// 模拟数据
const mockData = ref({
  productionBatches: 156,
  qualityTests: 89,
  materialStock: 234,
  activeUsers: 1247
});

// 快速操作配置
const quickActions = ref([
  {
    key: 'materials',
    title: '原料管理',
    description: '查看和管理原料信息',
    icon: 'Box',
    route: '/knowledge/materials',
    permissions: ['materials:view']
  },
  {
    key: 'production',
    title: '生产管理',
    description: '监控生产过程和状态',
    icon: 'Setting',
    route: '/production/planning',
    permissions: ['production:view']
  },
  {
    key: 'quality',
    title: '质量检测',
    description: '查看质量检测报告',
    icon: 'CircleCheck',
    route: '/traceability/production',
    permissions: ['quality:view']
  },
  {
    key: 'traceability',
    title: '产品溯源',
    description: '追踪产品全链路信息',
    icon: 'Link',
    route: '/traceability/materials',
    permissions: ['traceability:view']
  },
  {
    key: 'formulas',
    title: '配方管理',
    description: '管理产品配方信息',
    icon: 'Document',
    route: '/knowledge/formulas',
    permissions: ['formulas:view']
  },
  {
    key: 'analytics',
    title: '数据分析',
    description: '查看业务数据分析',
    icon: 'DataAnalysis',
    route: '/system/monitor',
    permissions: ['system:manage']
  }
]);

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '新批次生产完成',
    description: '批次号 TCM-2025-001 已完成生产，等待质量检测',
    timestamp: '2025-08-16 14:30',
    type: 'success',
    status: 'success',
    statusText: '已完成'
  },
  {
    id: 2,
    title: '原料库存预警',
    description: '当归库存不足，当前库存量：50kg，建议及时补充',
    timestamp: '2025-08-16 13:15',
    type: 'warning',
    status: 'warning',
    statusText: '需关注'
  },
  {
    id: 3,
    title: '质量检测通过',
    description: '批次号 TCM-2025-002 质量检测全部通过，可以发货',
    timestamp: '2025-08-16 11:45',
    type: 'success',
    status: 'success',
    statusText: '已通过'
  },
  {
    id: 4,
    title: '新用户注册',
    description: '消费者用户 "健康生活者" 完成注册并通过验证',
    timestamp: '2025-08-16 10:20',
    type: 'info',
    status: 'info',
    statusText: '已处理'
  }
]);

// 获取角色显示名称
const getRoleDisplayName = (role: string | undefined): string => {
  if (!role) return '未知角色';
  return ROLE_LABELS[role as keyof typeof ROLE_LABELS] || role;
};

// 获取当前时间问候语
const getCurrentTimeGreeting = (): string => {
  const hour = new Date().getHours();
  if (hour < 6) return '夜深了';
  if (hour < 9) return '早上好';
  if (hour < 12) return '上午好';
  if (hour < 14) return '中午好';
  if (hour < 18) return '下午好';
  if (hour < 22) return '晚上好';
  return '夜深了';
};

// 获取当前日期
const getCurrentDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const date = now.getDate();
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const weekday = weekdays[now.getDay()];
  
  return `${year}年${month}月${date}日 ${weekday}`;
};

// 检查操作权限
const hasActionPermission = (action: any): boolean => {
  if (!action.permissions || action.permissions.length === 0) {
    return true;
  }
  return authStore.hasAnyPermission(action.permissions);
};

// 处理快速操作点击
const handleQuickAction = (action: any) => {
  if (!hasActionPermission(action)) {
    ElMessage.warning('您没有执行此操作的权限');
    return;
  }

  if (action.route) {
    router.push(action.route);
  } else {
    ElMessage.info(`${action.title} 功能正在开发中...`);
  }
};

// 处理统计卡片点击
const handleStatClick = (type: string) => {
  const routeMap: Record<string, string> = {
    production: '/production/planning',
    quality: '/traceability/production',
    materials: '/knowledge/materials',
    users: '/dashboard/home' // 暂时跳转到仪表盘
  };

  const route = routeMap[type];
  if (route) {
    router.push(route);
  } else {
    ElMessage.info('功能正在开发中...');
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里加载仪表盘数据
  console.log('仪表盘已加载，当前用户:', authStore.user);
});
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 欢迎区域样式 */
.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.welcome-subtitle {
  margin: 0 0 8px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.welcome-time {
  margin: 0;
  color: #95a5a6;
  font-size: 14px;
}

.welcome-avatar {
  flex-shrink: 0;
}

/* 区域标题样式 */
.section-title {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* 快速操作区域样式 */
.quick-actions-section {
  margin-bottom: 32px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.quick-action-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.quick-action-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.quick-action-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-action-content {
  text-align: center;
  padding: 16px;
}

.action-icon {
  color: #409eff;
  margin-bottom: 12px;
}

.quick-action-content h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 500;
}

.quick-action-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 12px;
  line-height: 1.4;
}

/* 数据概览区域样式 */
.overview-section {
  margin-bottom: 32px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 8px;
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.overview-icon.production {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.quality {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.materials {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.customers {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-content h4 {
  margin: 0 0 4px 0;
  color: #7f8c8d;
  font-size: 14px;
  font-weight: 400;
}

.overview-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.overview-trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
}

.overview-trend.positive {
  color: #67c23a;
}

.overview-trend.negative {
  color: #f56c6c;
}

.overview-trend .el-icon {
  margin-right: 4px;
}

/* 最近活动区域样式 */
.recent-activities-section {
  margin-bottom: 32px;
}

.activities-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-content h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 500;
}

.activity-content p {
  margin: 0 0 8px 0;
  color: #7f8c8d;
  font-size: 13px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .section-title {
    font-size: 16px;
  }

  .welcome-text h2 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .overview-item {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .overview-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}

/* 动画效果 */
.dashboard-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.el-card {
  transition: all 0.3s ease;
}

.welcome-card:hover,
.overview-card:hover,
.activities-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
</style>
