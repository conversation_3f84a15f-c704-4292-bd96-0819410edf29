<template>
  <PageContainer
    title="数据安全管理"
    description="数据安全监控与管理平台"
    :breadcrumb="breadcrumb"
    icon="Lock"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAddSecurity">
        新增安全策略
      </el-button>
      <el-button :icon="Refresh" @click="handleSecurityScan">
        安全扫描
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出报告
      </el-button>
    </template>

    <!-- 安全状态概览 -->
    <div class="security-overview mb-xxl">
      <div class="overview-grid">
        <SecurityCard
          title="整体安全等级"
          :value="securityMetrics.overallLevel"
          icon="Lock"
          theme="primary"
          :status="securityMetrics.overallStatus"
          description="系统整体安全防护等级"
        />
        <SecurityCard
          title="数据加密率"
          :value="securityMetrics.encryptionRate"
          icon="Key"
          theme="success"
          suffix="%"
          :status="getEncryptionStatus(securityMetrics.encryptionRate)"
          description="敏感数据加密覆盖率"
        />
        <SecurityCard
          title="访问控制合规"
          :value="securityMetrics.accessControlCompliance"
          icon="UserFilled"
          theme="warning"
          suffix="%"
          :status="getComplianceStatus(securityMetrics.accessControlCompliance)"
          description="访问控制策略合规率"
        />
        <SecurityCard
          title="安全事件"
          :value="securityMetrics.securityIncidents"
          icon="Warning"
          theme="danger"
          :status="getIncidentStatus(securityMetrics.securityIncidents)"
          description="本月安全事件数量"
        />
      </div>
    </div>

    <!-- 安全级别导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>安全级别</h3>
        <div class="category-tabs">
          <el-button
            v-for="level in securityLevels"
            :key="level.value"
            :type="selectedLevel === level.value ? 'primary' : 'default'"
            @click="selectLevel(level.value)"
          >
            {{ level.label }}
          </el-button>
          <el-button
            :type="selectedLevel === '' ? 'primary' : 'default'"
            @click="selectLevel('')"
          >
            全部级别
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.complianceStatus"
            placeholder="合规状态"
            clearable
            @change="loadData"
          >
            <el-option label="完全合规" value="compliant" />
            <el-option label="部分合规" value="partial" />
            <el-option label="不合规" value="non_compliant" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索关键词"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 安全监控图表 -->
    <div class="charts-section mb-lg">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-card title="安全事件趋势">
            <div ref="incidentTrendChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card title="漏洞分布">
            <div ref="vulnerabilityChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="16" class="mt-lg">
        <el-col :span="8">
          <el-card title="数据分类分布">
            <div ref="dataClassificationChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card title="访问控制状态">
            <div ref="accessControlChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card title="合规性评分">
            <div ref="complianceChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据安全管理列表 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="数据安全管理"
      description="查看和管理所有数据安全策略"
      :show-selection="true"
      :show-index="true"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 安全级别列插槽 -->
      <template #securityLevel="{ row }">
        <el-tag :type="getSecurityLevelType(row.securityLevel)">
          {{ getSecurityLevelLabel(row.securityLevel) }}
        </el-tag>
      </template>

      <!-- 数据分类列插槽 -->
      <template #dataClassification="{ row }">
        <div class="classification-summary">
          <el-badge :value="row.dataClassification.length" :max="99">
            <el-button size="small" :icon="Files">
              数据类型
            </el-button>
          </el-badge>
        </div>
      </template>

      <!-- 加密状态列插槽 -->
      <template #encryptionStatus="{ row }">
        <div class="encryption-status">
          <div class="encryption-rate">
            {{ row.encryptionStatus.encryptedDataPercentage }}%
          </div>
          <el-tag size="small" :type="getEncryptionStatusType(row.encryptionStatus.overallStatus)">
            {{ getEncryptionStatusLabel(row.encryptionStatus.overallStatus) }}
          </el-tag>
        </div>
      </template>

      <!-- 访问控制列插槽 -->
      <template #accessControls="{ row }">
        <div class="access-controls">
          <el-badge :value="row.accessControls.length" :max="99">
            <el-button size="small" :icon="UserFilled">
              控制策略
            </el-button>
          </el-badge>
        </div>
      </template>

      <!-- 安全事件列插槽 -->
      <template #securityIncidents="{ row }">
        <div class="security-incidents">
          <el-badge :value="row.securityIncidents.length" :max="99" :type="getIncidentBadgeType(row.securityIncidents)">
            <el-button size="small" :icon="Warning">
              安全事件
            </el-button>
          </el-badge>
        </div>
      </template>

      <!-- 合规状态列插槽 -->
      <template #complianceStatus="{ row }">
        <div class="compliance-status">
          <div class="compliance-score">
            {{ row.complianceStatus.overallComplianceScore }}分
          </div>
          <el-tag size="small" :type="getComplianceScoreType(row.complianceStatus.overallComplianceScore)">
            {{ getComplianceScoreLabel(row.complianceStatus.overallComplianceScore) }}
          </el-tag>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="info"
          size="small"
          :icon="Setting"
          @click="handleConfig(row)"
        >
          配置
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="Monitor"
          @click="handleMonitor(row)"
        >
          监控
        </el-button>
      </template>
    </DataTable>

    <!-- 安全详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="数据安全详情"
      width="1400px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>安全基本信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="安全级别">
              <el-tag :type="getSecurityLevelType(currentRow.securityLevel)">
                {{ getSecurityLevelLabel(currentRow.securityLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后安全审查">
              {{ formatDate(currentRow.lastSecurityReview) }}
            </el-descriptions-item>
            <el-descriptions-item label="下次安全审查">
              {{ formatDate(currentRow.nextSecurityReview) }}
            </el-descriptions-item>
            <el-descriptions-item label="数据加密率">
              {{ currentRow.encryptionStatus.encryptedDataPercentage }}%
            </el-descriptions-item>
            <el-descriptions-item label="备份覆盖率">
              {{ currentRow.backupStatus.backupCoverage }}%
            </el-descriptions-item>
            <el-descriptions-item label="合规评分">
              {{ currentRow.complianceStatus.overallComplianceScore }}分
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 数据分类 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>数据分类管理</h3>
          </template>
          <el-table :data="currentRow.dataClassification" border>
            <el-table-column prop="dataType" label="数据类型" width="150" />
            <el-table-column prop="classificationLevel" label="分类级别" width="120">
              <template #default="{ row }">
                <el-tag :type="getSecurityLevelType(row.classificationLevel)">
                  {{ getSecurityLevelLabel(row.classificationLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dataVolume" label="数据量" width="100" align="right">
              <template #default="{ row }">
                {{ row.dataVolume.toLocaleString() }}
              </template>
            </el-table-column>
            <el-table-column prop="storageLocation" label="存储位置" width="120" />
            <el-table-column prop="retentionPeriod" label="保留期限" width="100" />
            <el-table-column prop="encryptionRequired" label="加密要求" width="100">
              <template #default="{ row }">
                <el-tag :type="row.encryptionRequired ? 'success' : 'info'">
                  {{ row.encryptionRequired ? '必须' : '可选' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" min-width="200" />
          </el-table>
        </el-card>

        <!-- 加密状态 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>数据加密状态</h3>
          </template>
          <div class="encryption-overview">
            <div class="encryption-summary">
              <div class="summary-item">
                <div class="summary-label">整体状态</div>
                <div class="summary-value">
                  <el-tag size="large" :type="getEncryptionStatusType(currentRow.encryptionStatus.overallStatus)">
                    {{ getEncryptionStatusLabel(currentRow.encryptionStatus.overallStatus) }}
                  </el-tag>
                </div>
              </div>
              <div class="summary-item">
                <div class="summary-label">加密覆盖率</div>
                <div class="summary-value">
                  <span class="percentage">{{ currentRow.encryptionStatus.encryptedDataPercentage }}%</span>
                  <el-progress
                    :percentage="currentRow.encryptionStatus.encryptedDataPercentage"
                    :stroke-width="8"
                    :color="getEncryptionProgressColor(currentRow.encryptionStatus.encryptedDataPercentage)"
                  />
                </div>
              </div>
            </div>
            <div class="encryption-methods">
              <h4>加密方法</h4>
              <el-table :data="currentRow.encryptionStatus.encryptionMethods" border>
                <el-table-column prop="methodName" label="方法名称" width="200" />
                <el-table-column prop="algorithm" label="加密算法" width="150" />
                <el-table-column prop="keyLength" label="密钥长度" width="100" align="right">
                  <template #default="{ row }">
                    {{ row.keyLength }} bit
                  </template>
                </el-table-column>
                <el-table-column prop="implementationDate" label="实施日期" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                      {{ row.status === 'active' ? '活跃' : '非活跃' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="dataTypes" label="适用数据类型" min-width="200">
                  <template #default="{ row }">
                    <el-tag
                      v-for="type in row.dataTypes"
                      :key="type"
                      size="small"
                      class="mr-xs"
                    >
                      {{ type }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>

        <!-- 访问控制 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>访问控制管理</h3>
          </template>
          <div class="access-control-list">
            <div
              v-for="control in currentRow.accessControls"
              :key="control.resourceId"
              class="access-control-item"
            >
              <div class="control-header">
                <h4>{{ control.resourceName }}</h4>
                <div class="control-meta">
                  <el-tag>{{ control.resourceType }}</el-tag>
                  <el-tag :type="getSecurityLevelType(control.accessLevel)">
                    {{ getSecurityLevelLabel(control.accessLevel) }}
                  </el-tag>
                </div>
              </div>
              <div class="control-stats">
                <div class="stat-item">
                  <span class="stat-label">授权用户:</span>
                  <span class="stat-value">{{ control.authorizedUsers.length }}人</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">访问次数:</span>
                  <span class="stat-value">{{ control.accessCount.toLocaleString() }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">失败尝试:</span>
                  <span class="stat-value">{{ control.failedAttempts }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最后访问:</span>
                  <span class="stat-value">{{ formatDateTime(control.lastAccessed) }}</span>
                </div>
              </div>
              <div class="authorized-users">
                <h5>授权用户</h5>
                <el-table :data="control.authorizedUsers" border size="small">
                  <el-table-column prop="userName" label="用户名" width="100" />
                  <el-table-column prop="role" label="角色" width="120" />
                  <el-table-column prop="department" label="部门" width="120" />
                  <el-table-column prop="accessLevel" label="访问级别" width="100">
                    <template #default="{ row }">
                      <el-tag size="small">{{ row.accessLevel }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="grantedDate" label="授权日期" width="120" />
                  <el-table-column prop="expiryDate" label="到期日期" width="120" />
                  <el-table-column prop="lastAccess" label="最后访问" width="150">
                    <template #default="{ row }">
                      {{ formatDateTime(row.lastAccess) }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 安全事件 -->
        <el-card v-if="currentRow.securityIncidents.length > 0" class="detail-card mb-lg">
          <template #header>
            <h3>安全事件记录</h3>
          </template>
          <el-table :data="currentRow.securityIncidents" border>
            <el-table-column prop="incidentType" label="事件类型" width="150" />
            <el-table-column prop="severity" label="严重程度" width="100">
              <template #default="{ row }">
                <el-tag :type="getSeverityType(row.severity)">
                  {{ getSeverityLabel(row.severity) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getIncidentStatusType(row.status)">
                  {{ getIncidentStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="detectedTime" label="检测时间" width="150">
              <template #default="{ row }">
                {{ formatDateTime(row.detectedTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="affectedUsers" label="影响用户" width="100" align="right" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column prop="assignedTo" label="负责人" width="100" />
          </el-table>
        </el-card>

        <!-- 漏洞评估 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>漏洞评估结果</h3>
          </template>
          <div class="vulnerability-assessment">
            <div class="assessment-overview">
              <div class="overview-item">
                <div class="overview-label">整体风险评分</div>
                <div class="overview-value">
                  <span class="risk-score">{{ currentRow.vulnerabilityAssessment.overallRiskScore }}</span>
                  <el-tag :type="getRiskScoreType(currentRow.vulnerabilityAssessment.overallRiskScore)">
                    {{ getRiskScoreLabel(currentRow.vulnerabilityAssessment.overallRiskScore) }}
                  </el-tag>
                </div>
              </div>
              <div class="vulnerability-stats">
                <div class="vuln-stat critical">
                  <div class="vuln-count">{{ currentRow.vulnerabilityAssessment.criticalVulnerabilities }}</div>
                  <div class="vuln-label">严重</div>
                </div>
                <div class="vuln-stat high">
                  <div class="vuln-count">{{ currentRow.vulnerabilityAssessment.highVulnerabilities }}</div>
                  <div class="vuln-label">高危</div>
                </div>
                <div class="vuln-stat medium">
                  <div class="vuln-count">{{ currentRow.vulnerabilityAssessment.mediumVulnerabilities }}</div>
                  <div class="vuln-label">中危</div>
                </div>
                <div class="vuln-stat low">
                  <div class="vuln-count">{{ currentRow.vulnerabilityAssessment.lowVulnerabilities }}</div>
                  <div class="vuln-label">低危</div>
                </div>
              </div>
            </div>
            <div v-if="currentRow.vulnerabilityAssessment.vulnerabilities.length > 0" class="vulnerability-list">
              <h4>漏洞详情</h4>
              <el-table :data="currentRow.vulnerabilityAssessment.vulnerabilities" border>
                <el-table-column prop="title" label="漏洞标题" width="200" />
                <el-table-column prop="severity" label="严重程度" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getSeverityType(row.severity)">
                      {{ getSeverityLabel(row.severity) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="cvssScore" label="CVSS评分" width="100" align="right" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getVulnStatusType(row.status)">
                      {{ getVulnStatusLabel(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="discoveryDate" label="发现日期" width="120" />
                <el-table-column prop="assignedTo" label="负责人" width="100" />
                <el-table-column prop="dueDate" label="截止日期" width="120" />
                <el-table-column prop="description" label="描述" min-width="200" />
              </el-table>
            </div>
          </div>
        </el-card>

        <!-- 合规状态 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>合规状态评估</h3>
          </template>
          <div class="compliance-assessment">
            <div class="compliance-overview">
              <div class="compliance-score-display">
                <div class="score-label">整体合规评分</div>
                <div class="score-value">{{ currentRow.complianceStatus.overallComplianceScore }}</div>
                <div class="score-status">
                  <el-tag size="large" :type="getComplianceScoreType(currentRow.complianceStatus.overallComplianceScore)">
                    {{ getComplianceScoreLabel(currentRow.complianceStatus.overallComplianceScore) }}
                  </el-tag>
                </div>
              </div>
              <div class="compliance-frameworks">
                <h4>合规框架</h4>
                <div class="framework-list">
                  <div
                    v-for="framework in currentRow.complianceStatus.complianceFrameworks"
                    :key="framework.frameworkId"
                    class="framework-item"
                  >
                    <div class="framework-header">
                      <h5>{{ framework.frameworkName }}</h5>
                      <el-tag>{{ framework.version }}</el-tag>
                    </div>
                    <div class="framework-score">
                      <span class="score">{{ framework.complianceScore }}%</span>
                      <el-progress
                        :percentage="framework.complianceScore"
                        :stroke-width="6"
                        :color="getComplianceProgressColor(framework.complianceScore)"
                      />
                    </div>
                    <div class="framework-controls">
                      <div class="control-stat">
                        <span class="label">已实施:</span>
                        <span class="value">{{ framework.implementedControls }}</span>
                      </div>
                      <div class="control-stat">
                        <span class="label">部分实施:</span>
                        <span class="value">{{ framework.partiallyImplementedControls }}</span>
                      </div>
                      <div class="control-stat">
                        <span class="label">未实施:</span>
                        <span class="value">{{ framework.notImplementedControls }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="currentRow.complianceStatus.complianceGaps.length > 0" class="compliance-gaps">
              <h4>合规差距</h4>
              <el-table :data="currentRow.complianceStatus.complianceGaps" border>
                <el-table-column prop="controlName" label="控制项" width="200" />
                <el-table-column prop="framework" label="框架" width="150" />
                <el-table-column prop="currentStatus" label="当前状态" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getImplementationStatusType(row.currentStatus)">
                      {{ getImplementationStatusLabel(row.currentStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="riskLevel" label="风险等级" width="100">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getRiskLevelType(row.riskLevel)">
                      {{ row.riskLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="estimatedCost" label="预估成本" width="120" align="right">
                  <template #default="{ row }">
                    ¥{{ row.estimatedCost.toLocaleString() }}
                  </template>
                </el-table-column>
                <el-table-column prop="targetDate" label="目标日期" width="120" />
                <el-table-column prop="assignedTo" label="负责人" width="100" />
                <el-table-column prop="gapDescription" label="差距描述" min-width="200" />
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleConfig(currentRow)">配置安全策略</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Lock,
  Plus,
  Refresh,
  Download,
  Search,
  View,
  Setting,
  Monitor,
  Key,
  UserFilled,
  Warning,
  Files
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import SecurityCard from '../../components/common/SecurityCard.vue';
import {
  getDataSecurityManagement,
  securityLevels,
  incidentStatuses,
  vulnerabilitySeverities,
  type DataSecurityManagement
} from '../../mock/security-compliance';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '安全与合规', path: '/security' },
  { title: '数据安全管理', path: '/security/data-security' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<DataSecurityManagement | null>(null);
const tableRef = ref();
const selectedLevel = ref('');

// 图表引用
const incidentTrendChart = ref();
const vulnerabilityChart = ref();
const dataClassificationChart = ref();
const accessControlChart = ref();
const complianceChart = ref();

// 表格数据
const tableData = ref<DataSecurityManagement[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  securityLevel: '',
  complianceStatus: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 安全指标
const securityMetrics = computed(() => {
  const data = tableData.value[0];
  if (!data) {
    return {
      overallLevel: '机密',
      overallStatus: 'secure',
      encryptionRate: 0,
      accessControlCompliance: 0,
      securityIncidents: 0
    };
  }
  
  return {
    overallLevel: getSecurityLevelLabel(data.securityLevel),
    overallStatus: 'secure',
    encryptionRate: data.encryptionStatus.encryptedDataPercentage,
    accessControlCompliance: 95.5,
    securityIncidents: data.securityIncidents.length
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'id',
    label: '安全ID',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'securityLevel',
    label: '安全级别',
    width: 120,
    slot: 'securityLevel'
  },
  {
    prop: 'dataClassification',
    label: '数据分类',
    width: 120,
    slot: 'dataClassification'
  },
  {
    prop: 'encryptionStatus',
    label: '加密状态',
    width: 150,
    slot: 'encryptionStatus'
  },
  {
    prop: 'accessControls',
    label: '访问控制',
    width: 120,
    slot: 'accessControls'
  },
  {
    prop: 'securityIncidents',
    label: '安全事件',
    width: 120,
    slot: 'securityIncidents'
  },
  {
    prop: 'complianceStatus',
    label: '合规状态',
    width: 150,
    slot: 'complianceStatus'
  },
  {
    prop: 'lastSecurityReview',
    label: '最后审查',
    width: 120,
    formatter: (row: DataSecurityManagement) => formatDate(row.lastSecurityReview)
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getDataSecurityManagement({
      securityLevel: filters.securityLevel || selectedLevel.value,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;

    // 加载图表数据
    await nextTick();
    initCharts();
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.securityLevel = '';
  filters.complianceStatus = '';
  filters.dateRange = null;
  filters.keyword = '';
  selectedLevel.value = '';
  loadData();
};

const selectLevel = (level: string) => {
  selectedLevel.value = level;
  filters.securityLevel = level;
  loadData();
};

const getSecurityLevelLabel = (level: string) => {
  const item = securityLevels.find(l => l.value === level);
  return item ? item.label : level;
};

const getSecurityLevelType = (level: string) => {
  const item = securityLevels.find(l => l.value === level);
  return item ? item.type : 'info';
};

const getEncryptionStatus = (rate: number) => {
  if (rate >= 95) return 'excellent';
  if (rate >= 85) return 'good';
  if (rate >= 70) return 'fair';
  return 'poor';
};

const getComplianceStatus = (score: number) => {
  if (score >= 95) return 'excellent';
  if (score >= 85) return 'good';
  if (score >= 70) return 'fair';
  return 'poor';
};

const getIncidentStatus = (count: number) => {
  if (count === 0) return 'safe';
  if (count <= 2) return 'low';
  if (count <= 5) return 'medium';
  return 'high';
};

const getEncryptionStatusLabel = (status: string) => {
  const statusMap = {
    'compliant': '合规',
    'partial': '部分合规',
    'non_compliant': '不合规'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const getEncryptionStatusType = (status: string) => {
  const typeMap = {
    'compliant': 'success',
    'partial': 'warning',
    'non_compliant': 'danger'
  };
  return typeMap[status as keyof typeof typeMap] || 'info';
};

const getIncidentBadgeType = (incidents: any[]) => {
  const count = incidents.length;
  if (count === 0) return 'success';
  if (count <= 2) return 'warning';
  return 'danger';
};

const getComplianceScoreLabel = (score: number) => {
  if (score >= 95) return '优秀';
  if (score >= 85) return '良好';
  if (score >= 70) return '一般';
  return '需改进';
};

const getComplianceScoreType = (score: number) => {
  if (score >= 95) return 'success';
  if (score >= 85) return 'primary';
  if (score >= 70) return 'warning';
  return 'danger';
};

const getSeverityLabel = (severity: string) => {
  const item = vulnerabilitySeverities.find(s => s.value === severity);
  return item ? item.label : severity;
};

const getSeverityType = (severity: string) => {
  const item = vulnerabilitySeverities.find(s => s.value === severity);
  return item ? item.type : 'info';
};

const getIncidentStatusLabel = (status: string) => {
  const item = incidentStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getIncidentStatusType = (status: string) => {
  const item = incidentStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getRiskScoreLabel = (score: number) => {
  if (score >= 8) return '高风险';
  if (score >= 6) return '中风险';
  if (score >= 4) return '低风险';
  return '极低风险';
};

const getRiskScoreType = (score: number) => {
  if (score >= 8) return 'danger';
  if (score >= 6) return 'warning';
  if (score >= 4) return 'primary';
  return 'success';
};

const getVulnStatusLabel = (status: string) => {
  const statusMap = {
    'new': '新发现',
    'in_progress': '处理中',
    'resolved': '已解决',
    'verified': '已验证',
    'closed': '已关闭'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const getVulnStatusType = (status: string) => {
  const typeMap = {
    'new': 'danger',
    'in_progress': 'warning',
    'resolved': 'success',
    'verified': 'primary',
    'closed': 'info'
  };
  return typeMap[status as keyof typeof typeMap] || 'info';
};

const getImplementationStatusLabel = (status: string) => {
  const statusMap = {
    'fully_implemented': '完全实施',
    'partially_implemented': '部分实施',
    'not_implemented': '未实施'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const getImplementationStatusType = (status: string) => {
  const typeMap = {
    'fully_implemented': 'success',
    'partially_implemented': 'warning',
    'not_implemented': 'danger'
  };
  return typeMap[status as keyof typeof typeMap] || 'info';
};

const getRiskLevelType = (level: string) => {
  const typeMap = {
    'low': 'success',
    'medium': 'warning',
    'high': 'danger'
  };
  return typeMap[level as keyof typeof typeMap] || 'info';
};

const getEncryptionProgressColor = (percentage: number) => {
  if (percentage >= 95) return '#67c23a';
  if (percentage >= 85) return '#e6a23c';
  return '#f56c6c';
};

const getComplianceProgressColor = (score: number) => {
  if (score >= 95) return '#67c23a';
  if (score >= 85) return '#409eff';
  if (score >= 70) return '#e6a23c';
  return '#f56c6c';
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const initCharts = () => {
  // 这里可以初始化图表
  // 由于没有实际的图表库，这里只是占位
  console.log('初始化安全监控图表');
};

const handleAddSecurity = () => {
  ElMessageBox.prompt('请输入安全策略名称', '新增安全策略', {
    confirmButtonText: '创建',
    cancelButtonText: '取消',
    inputPattern: /\S+/,
    inputErrorMessage: '策略名称不能为空'
  }).then(({ value }) => {
    ElMessage.success(`安全策略 "${value}" 创建成功`);
  }).catch(() => {
    // 用户取消
  });
};

const handleSecurityScan = () => {
  ElMessage.info('正在执行安全扫描...');
  setTimeout(() => {
    ElMessage.success('安全扫描完成');
  }, 2000);
};

const handleExport = () => {
  ElNotification({
    title: '导出成功',
    message: '安全报告已导出到 security_report.pdf',
    type: 'success',
    duration: 3000
  });
};

const handleView = (row: DataSecurityManagement) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleConfig = (row: DataSecurityManagement) => {
  ElMessage.info(`配置安全策略: ${row.id}`);
};

const handleMonitor = (row: DataSecurityManagement) => {
  ElMessage.info(`安全监控: ${row.id}`);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const handleSelectionChange = (selection: DataSecurityManagement[]) => {
  console.log('选中的安全策略:', selection);
};

const handleRowClick = (row: DataSecurityManagement) => {
  console.log('点击的安全策略:', row);
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 安全概览 */
.security-overview {
  margin-bottom: var(--spacing-xxl);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-input,
.filter-item .el-select,
.filter-item .el-date-picker {
  width: 100%;
}

/* 图表区域 */
.charts-section {
  margin-bottom: var(--spacing-lg);
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 表格插槽样式 */
.classification-summary,
.access-controls,
.security-incidents {
  display: flex;
  justify-content: center;
}

.encryption-status,
.compliance-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  align-items: center;
}

.encryption-rate,
.compliance-score {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 详情页面样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

/* 加密概览样式 */
.encryption-overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.encryption-summary {
  display: flex;
  gap: var(--spacing-xxl);
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-lighter);
  border-radius: var(--radius-medium);
}

.summary-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.summary-value {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.percentage {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.encryption-methods h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

/* 访问控制样式 */
.access-control-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.access-control-item {
  padding: var(--spacing-lg);
  background: var(--bg-lighter);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.control-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.control-meta {
  display: flex;
  gap: var(--spacing-sm);
}

.control-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  gap: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary);
}

.stat-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.authorized-users h5 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

/* 漏洞评估样式 */
.vulnerability-assessment {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.assessment-overview {
  display: flex;
  gap: var(--spacing-xxl);
}

.overview-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-lighter);
  border-radius: var(--radius-medium);
}

.overview-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.overview-value {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  align-items: center;
}

.risk-score {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.vulnerability-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.vuln-stat {
  text-align: center;
  padding: var(--spacing-md);
  border-radius: var(--radius-medium);
  min-width: 80px;
}

.vuln-stat.critical {
  background: var(--danger-color-light);
  border: 1px solid var(--danger-color);
}

.vuln-stat.high {
  background: var(--warning-color-light);
  border: 1px solid var(--warning-color);
}

.vuln-stat.medium {
  background: var(--primary-color-light);
  border: 1px solid var(--primary-color);
}

.vuln-stat.low {
  background: var(--success-color-light);
  border: 1px solid var(--success-color);
}

.vuln-count {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.vuln-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.vulnerability-list h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

/* 合规评估样式 */
.compliance-assessment {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.compliance-overview {
  display: flex;
  gap: var(--spacing-xxl);
}

.compliance-score-display {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-lighter);
  border-radius: var(--radius-medium);
}

.score-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.score-value {
  font-size: var(--font-size-xxxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.score-status {
  margin-top: var(--spacing-xs);
}

.compliance-frameworks {
  flex: 1;
}

.compliance-frameworks h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.framework-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.framework-item {
  padding: var(--spacing-md);
  background: var(--bg-lighter);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
}

.framework-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.framework-header h5 {
  margin: 0;
  color: var(--text-primary);
}

.framework-score {
  margin-bottom: var(--spacing-sm);
}

.framework-score .score {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.framework-controls {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.control-stat {
  display: flex;
  gap: var(--spacing-xs);
}

.control-stat .label {
  color: var(--text-secondary);
}

.control-stat .value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.compliance-gaps h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .encryption-summary,
  .assessment-overview,
  .compliance-overview {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .vulnerability-stats {
    flex-wrap: wrap;
    justify-content: center;
  }

  .control-stats,
  .framework-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .vulnerability-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 动画效果 */
.access-control-item,
.framework-item,
.vuln-stat {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
