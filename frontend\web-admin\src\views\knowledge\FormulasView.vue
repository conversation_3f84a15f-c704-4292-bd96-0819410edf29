<template>
  <PageContainer
    title="配方数据库"
    description="中药配方信息管理与研发数据库"
    :breadcrumb="breadcrumb"
    icon="Notebook"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建配方
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出数据
      </el-button>
      <el-button :icon="Upload" @click="handleImport">
        导入配方
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="配方总数"
          :value="stats.total"
          icon="Notebook"
          theme="primary"
          description="数据库中的配方总数量"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="生产中"
          :value="stats.production"
          icon="CircleCheck"
          theme="success"
          description="正在生产的配方数量"
          :clickable="true"
          @click="filterByStatus('production')"
        />
        <StatCard
          title="研发中"
          :value="stats.research"
          icon="Tools"
          theme="warning"
          description="正在研发的配方数量"
          :clickable="true"
          @click="filterByStatus('research')"
        />
        <StatCard
          title="已批准"
          :value="stats.approved"
          icon="CircleCheckFilled"
          theme="info"
          description="已获得批准的配方数量"
          :clickable="true"
          @click="filterByStatus('approved')"
        />
      </div>
    </div>

    <!-- 配方类型导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>配方类型</h3>
        <div class="category-tabs">
          <el-button
            v-for="type in formulaTypes"
            :key="type.value"
            :type="selectedType === type.value ? 'primary' : 'default'"
            @click="selectType(type.value)"
          >
            {{ type.label }}
          </el-button>
          <el-button
            :type="selectedType === '' ? 'primary' : 'default'"
            @click="selectType('')"
          >
            全部类型
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.category"
            placeholder="选择分类"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="category in formulaCategories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.type"
            placeholder="选择类型"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="type in formulaTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in formulaStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索配方名称、编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="配方列表"
      description="查看和管理所有配方信息"
      :show-selection="true"
      :show-index="true"
      :search-fields="['name', 'code', 'description']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 分类列插槽 -->
      <template #category="{ row }">
        <el-tag type="info">
          {{ getCategoryLabel(row.category) }}
        </el-tag>
      </template>

      <!-- 类型列插槽 -->
      <template #type="{ row }">
        <el-tag>
          {{ getTypeLabel(row.type) }}
        </el-tag>
      </template>

      <!-- 开发阶段列插槽 -->
      <template #stage="{ row }">
        <el-tag :type="getStageType(row.developmentStage)">
          {{ getStageLabel(row.developmentStage) }}
        </el-tag>
      </template>

      <!-- 审批状态列插槽 -->
      <template #approval="{ row }">
        <el-tag :type="getApprovalType(row.approvalStatus)">
          {{ getApprovalLabel(row.approvalStatus) }}
        </el-tag>
      </template>

      <!-- 价格列插槽 -->
      <template #price="{ row }">
        <span class="price-text">¥{{ row.price }}</span>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          :icon="Delete"
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 配方详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="配方详情"
      width="1200px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="配方编码">
              {{ currentRow.code }}
            </el-descriptions-item>
            <el-descriptions-item label="配方名称">
              {{ currentRow.name }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              {{ getCategoryLabel(currentRow.category) }}
            </el-descriptions-item>
            <el-descriptions-item label="类型">
              {{ getTypeLabel(currentRow.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="剂型">
              {{ getDosageFormLabel(currentRow.dosageForm) }}
            </el-descriptions-item>
            <el-descriptions-item label="开发阶段">
              <el-tag :type="getStageType(currentRow.developmentStage)">
                {{ getStageLabel(currentRow.developmentStage) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开发者">
              {{ currentRow.developer }}
            </el-descriptions-item>
            <el-descriptions-item label="审批状态">
              <el-tag :type="getApprovalType(currentRow.approvalStatus)">
                {{ getApprovalLabel(currentRow.approvalStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="批准文号">
              {{ currentRow.approvalNumber || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="成本">
              ¥{{ currentRow.cost }}
            </el-descriptions-item>
            <el-descriptions-item label="售价">
              ¥{{ currentRow.price }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="3">
              {{ currentRow.description }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 配方组成 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>配方组成</h3>
          </template>
          <el-table :data="currentRow.ingredients" border>
            <el-table-column prop="materialName" label="原料名称" />
            <el-table-column prop="amount" label="用量" align="right">
              <template #default="scope">
                {{ scope.row.amount }} {{ scope.row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="percentage" label="占比" align="right">
              <template #default="scope">
                {{ scope.row.percentage }}%
              </template>
            </el-table-column>
            <el-table-column prop="function" label="功能" />
            <el-table-column prop="processingMethod" label="炮制方法" />
          </el-table>
        </el-card>

        <!-- 功效与适应症 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>功效与适应症</h3>
          </template>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-section">
                <h4>主要功效</h4>
                <div class="tag-list">
                  <el-tag
                    v-for="efficacy in currentRow.efficacy"
                    :key="efficacy"
                    type="success"
                    class="tag-item"
                  >
                    {{ efficacy }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-section">
                <h4>适应症</h4>
                <div class="tag-list">
                  <el-tag
                    v-for="indication in currentRow.indications"
                    :key="indication"
                    type="warning"
                    class="tag-item"
                  >
                    {{ indication }}
                  </el-tag>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-section">
                <h4>禁忌症</h4>
                <div class="tag-list">
                  <el-tag
                    v-for="contraindication in currentRow.contraindications"
                    :key="contraindication"
                    type="danger"
                    class="tag-item"
                  >
                    {{ contraindication }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 用法用量 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>用法用量</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用法用量">
              {{ currentRow.dosage }}
            </el-descriptions-item>
            <el-descriptions-item label="服用方法">
              {{ currentRow.administration }}
            </el-descriptions-item>
            <el-descriptions-item label="贮藏条件">
              {{ currentRow.storageConditions }}
            </el-descriptions-item>
            <el-descriptions-item label="保质期">
              {{ currentRow.shelfLife }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 质量标准 -->
        <el-card class="detail-card mb-lg" v-if="currentRow.qualityStandards.length > 0">
          <template #header>
            <h3>质量标准</h3>
          </template>
          <el-table :data="currentRow.qualityStandards" border>
            <el-table-column prop="type" label="检测类型">
              <template #default="scope">
                <el-tag>{{ getQualityTypeLabel(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="检测项目" />
            <el-table-column prop="specification" label="规格要求" />
            <el-table-column prop="testMethod" label="检测方法" />
            <el-table-column prop="acceptanceCriteria" label="接受标准" />
          </el-table>
        </el-card>

        <!-- 临床数据 -->
        <el-card class="detail-card" v-if="currentRow.clinicalData.length > 0">
          <template #header>
            <h3>临床数据</h3>
          </template>
          <el-table :data="currentRow.clinicalData" border>
            <el-table-column prop="studyType" label="研究类型" />
            <el-table-column prop="studyPhase" label="研究阶段" />
            <el-table-column prop="sampleSize" label="样本量" />
            <el-table-column prop="duration" label="研究周期" />
            <el-table-column prop="primaryEndpoint" label="主要终点" />
            <el-table-column prop="conclusion" label="结论" />
            <el-table-column prop="publicationDate" label="发表日期" />
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
      </template>
    </el-dialog>

    <!-- 新增/编辑配方对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      :title="editingId ? '编辑配方' : '新增配方'"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配方名称" prop="name">
              <el-input v-model="addForm.name" placeholder="请输入配方名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配方编号" prop="code">
              <el-input v-model="addForm.code" placeholder="请输入配方编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="配方类型" prop="type">
              <el-select v-model="addForm.type" placeholder="请选择配方类型" style="width: 100%">
                <el-option label="汤剂" value="decoction" />
                <el-option label="丸剂" value="pill" />
                <el-option label="散剂" value="powder" />
                <el-option label="膏剂" value="paste" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="addForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="研发中" value="development" />
                <el-option label="已发布" value="published" />
                <el-option label="已停用" value="discontinued" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="主要功效" prop="mainEffects">
          <el-input
            v-model="addForm.mainEffects"
            type="textarea"
            :rows="2"
            placeholder="请输入主要功效"
          />
        </el-form-item>
        <el-form-item label="配方描述" prop="description">
          <el-input
            v-model="addForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入配方描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveFormula" :loading="saving">
          {{ editingId ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Notebook,
  Plus,
  Download,
  Upload,
  Search,
  View,
  Edit,
  Delete,
  CircleCheck,
  CircleCheckFilled,
  Tools
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getFormulas,
  formulaCategories,
  formulaTypes,
  formulaStatuses,
  dosageForms,
  developmentStages,
  approvalStatuses,
  type Formula
} from '../../mock/formulas';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '数据标准与知识库', path: '/knowledge' },
  { title: '配方数据库', path: '/knowledge/formulas' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const addDialogVisible = ref(false);
const saving = ref(false);
const editingId = ref('');
const currentRow = ref<Formula | null>(null);
const tableRef = ref();
const selectedType = ref('');
const addFormRef = ref();

// 表格数据
const tableData = ref<Formula[]>([]);
const total = ref(0);

// 新增表单数据
const addForm = reactive({
  name: '',
  code: '',
  type: '',
  status: '',
  mainEffects: '',
  description: ''
});

// 表单验证规则
const addFormRules = {
  name: [
    { required: true, message: '请输入配方名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入配方编号', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配方类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  mainEffects: [
    { required: true, message: '请输入主要功效', trigger: 'blur' }
  ]
};

// 筛选器
const filters = reactive({
  category: '',
  type: '',
  status: '',
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const formulas = tableData.value;
  return {
    total: formulas.length,
    production: formulas.filter(f => f.status === 'active').length,
    research: formulas.filter(f => f.status === 'development').length,
    approved: formulas.filter(f => f.approvalStatus === 'approved').length
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'code',
    label: '编码',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'name',
    label: '配方名称',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'category',
    label: '分类',
    width: 100,
    slot: 'category'
  },
  {
    prop: 'type',
    label: '类型',
    width: 120,
    slot: 'type'
  },
  {
    prop: 'developmentStage',
    label: '开发阶段',
    width: 100,
    slot: 'stage'
  },
  {
    prop: 'approvalStatus',
    label: '审批状态',
    width: 100,
    slot: 'approval'
  },
  {
    prop: 'developer',
    label: '开发者',
    minWidth: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'price',
    label: '售价',
    width: 100,
    slot: 'price',
    align: 'right'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getFormulas({
      category: filters.category,
      type: filters.type,
      status: filters.status,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.category = '';
  filters.type = '';
  filters.status = '';
  filters.keyword = '';
  selectedType.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const selectType = (type: string) => {
  selectedType.value = type;
  filters.type = type;
  loadData();
};

const handleImport = () => {
  ElMessage.success('配方导入功能已启动');
  // 这里可以添加实际的导入逻辑
};

const getCategoryLabel = (category: string) => {
  const item = formulaCategories.find(c => c.value === category);
  return item ? item.label : category;
};

const getTypeLabel = (type: string) => {
  const item = formulaTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getDosageFormLabel = (form: string) => {
  const item = dosageForms.find(f => f.value === form);
  return item ? item.label : form;
};

const getStageLabel = (stage: string) => {
  const item = developmentStages.find(s => s.value === stage);
  return item ? item.label : stage;
};

const getStageType = (stage: string) => {
  const typeMap: Record<string, string> = {
    research: 'info',
    formulation: 'info',
    testing: 'warning',
    clinical: 'warning',
    approval: 'warning',
    production: 'success',
    market: 'success'
  };
  return typeMap[stage] || 'info';
};

const getApprovalLabel = (status: string) => {
  const item = approvalStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getApprovalType = (status: string) => {
  const item = approvalStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getStatusLabel = (status: string) => {
  const item = formulaStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = formulaStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getQualityTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    appearance: '性状',
    physical: '理化',
    chemical: '化学',
    microbial: '微生物',
    stability: '稳定性'
  };
  return labelMap[type] || type;
};

const handleAdd = () => {
  editingId.value = '';
  Object.assign(addForm, {
    name: '',
    code: '',
    type: '',
    status: '',
    mainEffects: '',
    description: ''
  });
  addDialogVisible.value = true;
};

const handleEdit = (row: Formula) => {
  ElMessage.info(`编辑配方: ${row.name}`);
  detailVisible.value = false;
};

const handleView = (row: Formula) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleDelete = async (row: Formula) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配方 "${row.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    ElMessage.success('删除成功');
    loadData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleExport = () => {
  ElMessage.success('配方数据导出已开始');
  // 这里可以添加实际的导出逻辑
};

const handleSaveFormula = async () => {
  if (!addFormRef.value) return;

  try {
    await addFormRef.value.validate();
    saving.value = true;

    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success(editingId.value ? '配方更新成功' : '配方添加成功');
    addDialogVisible.value = false;
    loadData(); // 重新加载数据
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    saving.value = false;
  }
};

const handleSelectionChange = (selection: Formula[]) => {
  console.log('选中的配方:', selection);
};

const handleRowClick = (row: Formula) => {
  console.log('点击的配方:', row);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input {
  width: 100%;
}

/* 价格样式 */
.price-text {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 信息区域样式 */
.info-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tag-item {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 表格内容样式 */
:deep(.el-table) {
  .price-text {
    display: block;
    text-align: right;
  }
}

/* 对话框样式覆盖 */
:deep(.el-dialog) {
  .el-dialog__body {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
}

/* 动画效果 */
.detail-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
