<template>
  <PageContainer
    title="生产预警系统"
    description="实时监控和管理生产过程中的各类预警"
    :breadcrumb="breadcrumb"
    icon="Warning"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleCreateRule">
        新建预警规则
      </el-button>
      <el-button :icon="Setting" @click="handleSettings">
        预警设置
      </el-button>
    </template>

    <!-- 预警统计面板 -->
    <div class="alerts-panel mb-xxl">
      <div class="panel-grid">
        <StatCard
          title="活跃预警"
          :value="stats.active"
          icon="Warning"
          theme="danger"
          description="当前需要处理的预警"
          :clickable="true"
          @click="filterByStatus('active')"
        />
        <StatCard
          title="严重预警"
          :value="stats.critical"
          icon="CircleClose"
          theme="danger"
          description="严重级别的预警数量"
          :clickable="true"
          @click="filterBySeverity('critical')"
        />
        <StatCard
          title="今日预警"
          :value="stats.today"
          icon="Calendar"
          theme="warning"
          description="今日新增的预警数量"
          :clickable="true"
          @click="filterToday"
        />
        <StatCard
          title="平均响应时间"
          :value="stats.avgResponseTime"
          icon="Timer"
          theme="info"
          description="预警平均响应时间"
          suffix="分钟"
          :clickable="true"
          @click="showResponseAnalysis"
        />
      </div>
    </div>

    <!-- 实时预警横幅 -->
    <div v-if="criticalAlerts.length > 0" class="critical-alerts-banner mb-lg">
      <el-alert
        title="严重预警"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="critical-alerts-list">
            <div
              v-for="alert in criticalAlerts"
              :key="alert.id"
              class="critical-alert-item"
              @click="handleView(alert)"
            >
              <el-icon class="alert-icon"><Warning /></el-icon>
              <span class="alert-text">{{ alert.title }}</span>
              <el-tag type="danger" size="small">{{ alert.severity }}</el-tag>
            </div>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 预警类型导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>预警类型</h3>
        <div class="category-tabs">
          <el-button
            v-for="type in alertTypes"
            :key="type.value"
            :type="selectedType === type.value ? 'primary' : 'default'"
            @click="selectType(type.value)"
          >
            <el-icon><component :is="type.icon" /></el-icon>
            {{ type.label }}
          </el-button>
          <el-button
            :type="selectedType === '' ? 'primary' : 'default'"
            @click="selectType('')"
          >
            全部类型
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.severity"
            placeholder="选择严重程度"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="severity in severityLevels"
              :key="severity.value"
              :label="severity.label"
              :value="severity.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in alertStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索预警编码、标题"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="生产预警列表"
      description="查看和管理所有生产预警"
      :show-selection="true"
      :show-index="true"
      :search-fields="['alertCode', 'title', 'description']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 预警类型列插槽 -->
      <template #alertType="{ row }">
        <div class="alert-type">
          <el-icon :style="{ color: getAlertTypeColor(row.alertType) }">
            <component :is="getAlertTypeIcon(row.alertType)" />
          </el-icon>
          <span>{{ getAlertTypeLabel(row.alertType) }}</span>
        </div>
      </template>

      <!-- 严重程度列插槽 -->
      <template #severity="{ row }">
        <el-tag :type="getSeverityType(row.severity)">
          {{ getSeverityLabel(row.severity) }}
        </el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 来源列插槽 -->
      <template #source="{ row }">
        <div class="source-info">
          <div class="source-name">{{ row.source.sourceName }}</div>
          <div class="source-location">{{ row.source.location }}</div>
        </div>
      </template>

      <!-- 当前值列插槽 -->
      <template #currentValue="{ row }">
        <div v-if="row.currentValue !== undefined" class="value-info">
          <span class="value">{{ row.currentValue }}</span>
          <span v-if="row.unit" class="unit">{{ row.unit }}</span>
          <div v-if="row.thresholdValue" class="threshold">
            阈值: {{ row.thresholdValue }}{{ row.unit || '' }}
          </div>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status === 'active'"
          type="warning"
          size="small"
          :icon="Check"
          @click="handleAcknowledge(row)"
        >
          确认
        </el-button>
        <el-button
          v-if="row.status === 'acknowledged' || row.status === 'investigating'"
          type="success"
          size="small"
          :icon="CircleCheck"
          @click="handleResolve(row)"
        >
          解决
        </el-button>
      </template>
    </DataTable>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Warning,
  Plus,
  Setting,
  CircleClose,
  Calendar,
  Timer,
  Search,
  View,
  Check,
  CircleCheck
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getProductionAlerts,
  alertTypes,
  severityLevels,
  alertStatuses,
  type ProductionAlert
} from '../../mock/production-alerts';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '生产与运营管理', path: '/production' },
  { title: '生产预警', path: '/production/alerts' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<ProductionAlert | null>(null);
const tableRef = ref();
const selectedType = ref('');

// 表格数据
const tableData = ref<ProductionAlert[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  alertType: '',
  severity: '',
  status: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 严重预警
const criticalAlerts = computed(() => {
  return tableData.value.filter(alert =>
    alert.severity === 'critical' && alert.status === 'active'
  );
});

// 统计数据
const stats = computed(() => {
  const alerts = tableData.value;
  const today = new Date().toDateString();
  return {
    active: alerts.filter(a => a.status === 'active').length,
    critical: alerts.filter(a => a.severity === 'critical').length,
    today: alerts.filter(a => new Date(a.createdAt).toDateString() === today).length,
    avgResponseTime: '15' // 模拟数据
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'alertCode',
    label: '预警编码',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'alertType',
    label: '预警类型',
    width: 120,
    slot: 'alertType'
  },
  {
    prop: 'title',
    label: '预警标题',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'severity',
    label: '严重程度',
    width: 100,
    slot: 'severity'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'source',
    label: '来源',
    width: 150,
    slot: 'source'
  },
  {
    prop: 'currentValue',
    label: '当前值',
    width: 120,
    slot: 'currentValue'
  },
  {
    prop: 'assignedTo',
    label: '负责人',
    width: 100
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 120,
    formatter: (row: ProductionAlert) => formatDateTime(row.createdAt)
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getProductionAlerts({
      alertType: filters.alertType || selectedType.value,
      severity: filters.severity,
      status: filters.status,
      dateRange: filters.dateRange,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.alertType = '';
  filters.severity = '';
  filters.status = '';
  filters.dateRange = null;
  filters.keyword = '';
  selectedType.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const filterBySeverity = (severity: string) => {
  filters.severity = severity;
  loadData();
};

const filterToday = () => {
  const today = new Date().toISOString().split('T')[0];
  filters.dateRange = [today, today];
  loadData();
};

const selectType = (type: string) => {
  selectedType.value = type;
  filters.alertType = type;
  loadData();
};

const getAlertTypeLabel = (type: string) => {
  const item = alertTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getAlertTypeIcon = (type: string) => {
  const item = alertTypes.find(t => t.value === type);
  return item ? item.icon : 'Warning';
};

const getAlertTypeColor = (type: string) => {
  const item = alertTypes.find(t => t.value === type);
  return item ? item.color : '#409eff';
};

const getSeverityLabel = (severity: string) => {
  const item = severityLevels.find(s => s.value === severity);
  return item ? item.label : severity;
};

const getSeverityType = (severity: string) => {
  const item = severityLevels.find(s => s.value === severity);
  return item ? item.type : 'info';
};

const getStatusLabel = (status: string) => {
  const item = alertStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = alertStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const handleCreateRule = () => {
  ElMessage.info('新建预警规则功能开发中...');
};

const handleSettings = () => {
  ElMessageBox.alert(
    '预警设置包括：\n• 温度阈值设置\n• 压力监控配置\n• 设备状态预警\n• 通知方式设置\n• 预警级别定义',
    '预警设置',
    {
      confirmButtonText: '了解',
      type: 'info'
    }
  );
};

const handleView = (row: ProductionAlert) => {
  ElMessage.info(`查看预警详情: ${row.title}`);
};

const handleAcknowledge = (row: ProductionAlert) => {
  ElMessage.success(`预警已确认: ${row.title}`);
  // 更新状态
  row.status = 'acknowledged';
  row.acknowledgedBy = '当前用户';
  row.acknowledgedAt = new Date().toISOString();
};

const handleResolve = (row: ProductionAlert) => {
  ElMessage.success(`预警已解决: ${row.title}`);
  // 更新状态
  row.status = 'resolved';
  row.resolvedBy = '当前用户';
  row.resolvedAt = new Date().toISOString();
};

const handleSelectionChange = (selection: ProductionAlert[]) => {
  console.log('选中的预警:', selection);
};

const handleRowClick = (row: ProductionAlert) => {
  console.log('点击的预警:', row);
};

const showResponseAnalysis = () => {
  ElMessage.info('响应时间分析功能开发中...');
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 预警面板 */
.alerts-panel {
  margin-bottom: var(--spacing-xxl);
}

.panel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 严重预警横幅 */
.critical-alerts-banner {
  margin-bottom: var(--spacing-lg);
}

.critical-alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.critical-alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(245, 108, 108, 0.1);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: var(--transition-base);
}

.critical-alert-item:hover {
  background: rgba(245, 108, 108, 0.2);
}

.alert-icon {
  color: var(--danger-color);
  font-size: var(--font-size-lg);
}

.alert-text {
  flex: 1;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.category-tabs .el-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input,
.filter-item .el-date-picker {
  width: 100%;
}

/* 预警类型样式 */
.alert-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.alert-type .el-icon {
  font-size: var(--font-size-base);
}

/* 来源信息样式 */
.source-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.source-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.source-location {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 数值信息样式 */
.value-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.value {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.unit {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-left: var(--spacing-xs);
}

.threshold {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .critical-alerts-list {
    gap: var(--spacing-xs);
  }

  .critical-alert-item {
    padding: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .panel-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.critical-alert-item,
.alert-type,
.source-info,
.value-info {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 预警状态颜色 */
.el-tag.el-tag--danger {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
