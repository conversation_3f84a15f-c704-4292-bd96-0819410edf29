<template>
  <PageContainer
    title="用户管理"
    description="系统用户账户管理与权限配置"
    :breadcrumb="breadcrumb"
    icon="User"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAddUser">
        新增用户
      </el-button>
      <el-button :icon="Upload" @click="handleImportUsers">
        批量导入
      </el-button>
      <el-button :icon="Download" @click="handleExportUsers">
        导出用户
      </el-button>
    </template>

    <!-- 用户统计概览 -->
    <div class="user-overview mb-xxl">
      <div class="overview-grid">
        <MetricCard
          title="总用户数"
          :value="userMetrics.totalUsers"
          icon="User"
          theme="primary"
          description="系统注册用户总数"
        />
        <MetricCard
          title="活跃用户"
          :value="userMetrics.activeUsers"
          icon="UserFilled"
          theme="success"
          description="近30天活跃用户"
        />
        <MetricCard
          title="在线用户"
          :value="userMetrics.onlineUsers"
          icon="Connection"
          theme="warning"
          description="当前在线用户数"
        />
        <MetricCard
          title="新增用户"
          :value="userMetrics.newUsers"
          icon="Plus"
          theme="info"
          description="本月新增用户数"
        />
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.role"
            placeholder="用户角色"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="role in userRoles"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="用户状态"
            clearable
            @change="loadData"
          >
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="锁定" value="locked" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.department"
            placeholder="所属部门"
            clearable
            @change="loadData"
          >
            <el-option label="管理部" value="management" />
            <el-option label="生产部" value="production" />
            <el-option label="质控部" value="quality" />
            <el-option label="研发部" value="research" />
            <el-option label="销售部" value="sales" />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索用户"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="用户列表"
      description="查看和管理所有系统用户"
      :show-selection="true"
      :show-index="true"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 用户信息列插槽 -->
      <template #userInfo="{ row }">
        <div class="user-info">
          <div class="user-avatar">
            <el-avatar :src="row.avatar" :size="40">
              {{ row.realName.charAt(0) }}
            </el-avatar>
          </div>
          <div class="user-details">
            <div class="user-name">{{ row.realName }}</div>
            <div class="user-username">@{{ row.username }}</div>
          </div>
        </div>
      </template>

      <!-- 联系方式列插槽 -->
      <template #contact="{ row }">
        <div class="contact-info">
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <span>{{ row.email }}</span>
          </div>
          <div class="contact-item">
            <el-icon><Phone /></el-icon>
            <span>{{ row.phone }}</span>
          </div>
        </div>
      </template>

      <!-- 角色列插槽 -->
      <template #role="{ row }">
        <el-tag :type="getRoleType(row.role)">
          {{ getRoleLabel(row.role) }}
        </el-tag>
      </template>

      <!-- 部门职位列插槽 -->
      <template #department="{ row }">
        <div class="department-info">
          <div class="department">{{ row.department || '-' }}</div>
          <div class="position">{{ row.position || '-' }}</div>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 最后登录列插槽 -->
      <template #lastLogin="{ row }">
        <div class="last-login">
          <div v-if="row.lastLoginAt" class="login-time">
            {{ formatDateTime(row.lastLoginAt) }}
          </div>
          <div v-else class="no-login">从未登录</div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="info"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
          <el-button size="small" :icon="More" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
              <el-dropdown-item command="toggleStatus">
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-dropdown-item>
              <el-dropdown-item command="permissions">权限管理</el-dropdown-item>
              <el-dropdown-item command="loginHistory">登录历史</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除用户</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </DataTable>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="用户详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="user-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <div class="user-profile">
            <div class="profile-avatar">
              <el-avatar :src="currentRow.avatar" :size="80">
                {{ currentRow.realName.charAt(0) }}
              </el-avatar>
            </div>
            <div class="profile-info">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="真实姓名">
                  {{ currentRow.realName }}
                </el-descriptions-item>
                <el-descriptions-item label="用户名">
                  {{ currentRow.username }}
                </el-descriptions-item>
                <el-descriptions-item label="邮箱">
                  {{ currentRow.email }}
                </el-descriptions-item>
                <el-descriptions-item label="手机号">
                  {{ currentRow.phone }}
                </el-descriptions-item>
                <el-descriptions-item label="角色">
                  <el-tag :type="getRoleType(currentRow.role)">
                    {{ getRoleLabel(currentRow.role) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="getStatusType(currentRow.status)">
                    {{ getStatusLabel(currentRow.status) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="部门">
                  {{ currentRow.department || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="职位">
                  {{ currentRow.position || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ formatDateTime(currentRow.createdAt) }}
                </el-descriptions-item>
                <el-descriptions-item label="最后登录">
                  {{ currentRow.lastLoginAt ? formatDateTime(currentRow.lastLoginAt) : '从未登录' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-card>

        <!-- 权限信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>权限信息</h3>
          </template>
          <div class="permissions-info">
            <h4>角色权限</h4>
            <div class="permissions-grid">
              <el-tag
                v-for="permission in currentRow.permissions"
                :key="permission"
                size="small"
                class="permission-tag"
              >
                {{ getPermissionLabel(permission) }}
              </el-tag>
            </div>
          </div>
        </el-card>

        <!-- 活动记录 -->
        <el-card class="detail-card">
          <template #header>
            <h3>最近活动</h3>
          </template>
          <div class="activity-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="activity in userActivities"
                :key="activity.id"
                :timestamp="formatDateTime(activity.timestamp)"
                :type="getActivityType(activity.type)"
              >
                {{ activity.description }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑用户</el-button>
      </template>
    </el-dialog>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="editVisible"
      :title="editMode === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
      :before-close="handleCloseEdit"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="editForm.realName" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" :disabled="editMode === 'edit'" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="editForm.role" style="width: 100%">
            <el-option
              v-for="role in userRoles"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="editForm.department" />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="editForm.position" />
        </el-form-item>
        <el-form-item v-if="editMode === 'add'" label="初始密码" prop="password">
          <el-input v-model="editForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio label="active">活跃</el-radio>
            <el-radio label="inactive">非活跃</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveUser">保存</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  User,
  Plus,
  Upload,
  Download,
  Search,
  View,
  Edit,
  More,
  UserFilled,
  Connection,
  Message,
  Phone
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import MetricCard from '../../components/common/MetricCard.vue';
import type { UserInfo } from '../../types';
import { UserRole, ROLE_LABELS } from '../../types';
import { mockUsers } from '../../mock/users';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '系统管理', path: '/system' },
  { title: '用户管理', path: '/system/users' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const editVisible = ref(false);
const editMode = ref<'add' | 'edit'>('add');
const currentRow = ref<UserInfo | null>(null);
const tableRef = ref();
const editFormRef = ref();

// 表格数据
const tableData = ref<UserInfo[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  role: '',
  status: '',
  department: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 编辑表单
const editForm = reactive({
  realName: '',
  username: '',
  email: '',
  phone: '',
  role: UserRole.VIEWER,
  department: '',
  position: '',
  password: '',
  status: 'active' as 'active' | 'inactive'
});

// 表单验证规则
const editRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入初始密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
};

// 用户角色选项
const userRoles = computed(() => {
  return Object.entries(ROLE_LABELS).map(([value, label]) => ({
    value: value as UserRole,
    label
  }));
});

// 用户指标
const userMetrics = computed(() => {
  const users = tableData.value;
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  
  return {
    totalUsers: users.length,
    activeUsers: users.filter(u => u.status === 'active').length,
    onlineUsers: Math.floor(users.length * 0.15), // 模拟在线用户
    newUsers: users.filter(u => new Date(u.createdAt) >= thisMonth).length
  };
});

// 用户活动记录（模拟数据）
const userActivities = ref([
  {
    id: '1',
    type: 'login',
    description: '用户登录系统',
    timestamp: new Date().toISOString()
  },
  {
    id: '2',
    type: 'update',
    description: '更新个人资料',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: '3',
    type: 'action',
    description: '查看生产数据',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
  }
]);

// 表格列配置
const tableColumns = ref([
  {
    prop: 'id',
    label: '用户ID',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'userInfo',
    label: '用户信息',
    width: 200,
    slot: 'userInfo'
  },
  {
    prop: 'contact',
    label: '联系方式',
    width: 250,
    slot: 'contact'
  },
  {
    prop: 'role',
    label: '角色',
    width: 120,
    slot: 'role'
  },
  {
    prop: 'department',
    label: '部门/职位',
    width: 150,
    slot: 'department'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'lastLogin',
    label: '最后登录',
    width: 150,
    slot: 'lastLogin'
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 150,
    formatter: (row: UserInfo) => formatDate(row.createdAt)
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredData = [...mockUsers];

    // 角色筛选
    if (filters.role) {
      filteredData = filteredData.filter(user => user.role === filters.role);
    }

    // 状态筛选
    if (filters.status) {
      filteredData = filteredData.filter(user => user.status === filters.status);
    }

    // 部门筛选
    if (filters.department) {
      filteredData = filteredData.filter(user => user.department === filters.department);
    }

    // 关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filteredData = filteredData.filter(user =>
        user.realName.toLowerCase().includes(keyword) ||
        user.username.toLowerCase().includes(keyword) ||
        user.email.toLowerCase().includes(keyword)
      );
    }

    tableData.value = filteredData;
    total.value = filteredData.length;
  } catch (error) {
    ElMessage.error('加载用户数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.role = '';
  filters.status = '';
  filters.department = '';
  filters.dateRange = null;
  filters.keyword = '';
  loadData();
};

const getRoleLabel = (role: UserRole): string => {
  return ROLE_LABELS[role] || role;
};

const getRoleType = (role: UserRole): string => {
  const typeMap: Record<UserRole, string> = {
    [UserRole.SYSTEM_ADMIN]: 'danger',
    [UserRole.COMPANY_ADMIN]: 'warning',
    [UserRole.PRODUCTION_MANAGER]: 'primary',
    [UserRole.QUALITY_MANAGER]: 'success',
    [UserRole.R_AND_D_MANAGER]: 'info',
    [UserRole.SUPPLY_CHAIN_MANAGER]: 'primary',
    [UserRole.COMPLIANCE_MANAGER]: 'warning',
    [UserRole.OPERATOR]: 'info',
    [UserRole.QUALITY_INSPECTOR]: 'success',
    [UserRole.RESEARCHER]: 'info',
    [UserRole.VIEWER]: 'info'
  };
  return typeMap[role] || 'info';
};

const getStatusLabel = (status: string): string => {
  const statusMap = {
    'active': '活跃',
    'inactive': '非活跃',
    'locked': '锁定'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const getStatusType = (status: string): string => {
  const typeMap = {
    'active': 'success',
    'inactive': 'info',
    'locked': 'danger'
  };
  return typeMap[status as keyof typeof typeMap] || 'info';
};

const getPermissionLabel = (permission: string): string => {
  // 简化的权限标签映射
  const permissionMap: Record<string, string> = {
    'system:manage': '系统管理',
    'user:manage': '用户管理',
    'materials:view': '原料查看',
    'materials:create': '原料创建',
    'production:view': '生产查看',
    'quality:view': '质量查看'
  };
  return permissionMap[permission] || permission;
};

const getActivityType = (type: string): string => {
  const typeMap = {
    'login': 'primary',
    'update': 'success',
    'action': 'info',
    'error': 'danger'
  };
  return typeMap[type as keyof typeof typeMap] || 'info';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

const formatDateTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const handleAddUser = () => {
  editMode.value = 'add';
  resetEditForm();
  editVisible.value = true;
};

const handleEdit = (row: UserInfo) => {
  editMode.value = 'edit';
  currentRow.value = row;

  // 填充编辑表单
  Object.assign(editForm, {
    realName: row.realName,
    username: row.username,
    email: row.email,
    phone: row.phone,
    role: row.role,
    department: row.department || '',
    position: row.position || '',
    status: row.status
  });

  editVisible.value = true;
};

const handleView = (row: UserInfo) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleDropdownCommand = async (command: string, row: UserInfo) => {
  switch (command) {
    case 'resetPassword':
      await handleResetPassword(row);
      break;
    case 'toggleStatus':
      await handleToggleStatus(row);
      break;
    case 'permissions':
      handleManagePermissions(row);
      break;
    case 'loginHistory':
      handleViewLoginHistory(row);
      break;
    case 'delete':
      await handleDeleteUser(row);
      break;
  }
};

const handleResetPassword = async (row: UserInfo) => {
  try {
    await ElMessageBox.confirm(`确定要重置用户 ${row.realName} 的密码吗？`, '确认重置', {
      type: 'warning'
    });

    ElMessage.success('密码重置成功，新密码已发送到用户邮箱');
  } catch (error) {
    // 用户取消操作
  }
};

const handleToggleStatus = async (row: UserInfo) => {
  const action = row.status === 'active' ? '禁用' : '启用';
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${row.realName} 吗？`, `确认${action}`, {
      type: 'warning'
    });

    // 更新状态
    row.status = row.status === 'active' ? 'inactive' : 'active';
    ElMessage.success(`用户${action}成功`);
  } catch (error) {
    // 用户取消操作
  }
};

const handleManagePermissions = (row: UserInfo) => {
  ElMessage.success(`${row.realName} 的权限管理对话框已打开`);
  // 这里可以添加打开权限管理对话框的逻辑
};

const handleViewLoginHistory = (row: UserInfo) => {
  ElMessage.success(`${row.realName} 的登录历史已加载`);
  // 这里可以添加查看登录历史的逻辑
};

const handleDeleteUser = async (row: UserInfo) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 ${row.realName} 吗？此操作不可恢复。`, '确认删除', {
      type: 'danger'
    });

    // 从表格中移除用户
    const index = tableData.value.findIndex(u => u.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
    }

    ElMessage.success('用户删除成功');
  } catch (error) {
    // 用户取消操作
  }
};

const handleSaveUser = async () => {
  try {
    await editFormRef.value.validate();

    if (editMode.value === 'add') {
      // 新增用户
      const newUser: UserInfo = {
        id: `user_${Date.now()}`,
        username: editForm.username,
        email: editForm.email,
        phone: editForm.phone,
        realName: editForm.realName,
        role: editForm.role,
        permissions: [], // 根据角色设置权限
        department: editForm.department,
        position: editForm.position,
        status: editForm.status,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      tableData.value.unshift(newUser);
      ElMessage.success('用户创建成功');
    } else {
      // 编辑用户
      if (currentRow.value) {
        Object.assign(currentRow.value, {
          realName: editForm.realName,
          email: editForm.email,
          phone: editForm.phone,
          role: editForm.role,
          department: editForm.department,
          position: editForm.position,
          status: editForm.status,
          updatedAt: new Date().toISOString()
        });
      }
      ElMessage.success('用户信息更新成功');
    }

    editVisible.value = false;
  } catch (error) {
    // 表单验证失败
  }
};

const resetEditForm = () => {
  Object.assign(editForm, {
    realName: '',
    username: '',
    email: '',
    phone: '',
    role: UserRole.VIEWER,
    department: '',
    position: '',
    password: '',
    status: 'active'
  });
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const handleCloseEdit = () => {
  editVisible.value = false;
  currentRow.value = null;
  resetEditForm();
};

const handleImportUsers = () => {
  ElMessage.success('用户批量导入功能已启动');
  // 这里可以添加实际的导入逻辑
};

const handleExportUsers = () => {
  const data = tableData.value.map(user => ({
    用户ID: user.id,
    真实姓名: user.realName,
    用户名: user.username,
    邮箱: user.email,
    手机号: user.phone,
    角色: getRoleLabel(user.role),
    部门: user.department || '',
    职位: user.position || '',
    状态: getStatusLabel(user.status),
    创建时间: formatDateTime(user.createdAt),
    最后登录: user.lastLoginAt ? formatDateTime(user.lastLoginAt) : '从未登录'
  }));

  // 这里应该实现真正的导出功能
  console.log('导出用户数据:', data);
  ElMessage.success('用户数据导出成功');
};

const handleSelectionChange = (selection: UserInfo[]) => {
  console.log('选中的用户:', selection);
};

const handleRowClick = (row: UserInfo) => {
  console.log('点击的用户:', row);
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 用户概览 */
.user-overview {
  margin-bottom: var(--spacing-xxl);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-input,
.filter-item .el-select,
.filter-item .el-date-picker {
  width: 100%;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.user-username {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 联系方式样式 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

/* 部门信息样式 */
.department-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.department {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.position {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 最后登录样式 */
.last-login {
  font-size: var(--font-size-sm);
}

.login-time {
  color: var(--text-primary);
}

.no-login {
  color: var(--text-secondary);
}

/* 详情页面样式 */
.user-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.user-profile {
  display: flex;
  gap: var(--spacing-lg);
}

.profile-avatar {
  flex-shrink: 0;
}

.profile-info {
  flex: 1;
}

/* 权限信息样式 */
.permissions-info h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.permissions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.permission-tag {
  margin: 0;
}

/* 活动时间线样式 */
.activity-timeline {
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .user-profile {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.user-info,
.contact-info,
.department-info {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
