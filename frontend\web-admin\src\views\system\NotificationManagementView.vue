<template>
  <PageContainer
    title="通知管理"
    description="系统通知发送、模板管理与消息中心"
    :breadcrumb="breadcrumb"
    icon="Bell"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="createNotification">
        发送通知
      </el-button>
      <el-button :icon="Setting" @click="showTemplateManager">
        模板管理
      </el-button>
      <el-button :icon="Refresh" @click="refreshData">
        刷新数据
      </el-button>
    </template>

    <!-- 通知统计概览 -->
    <div class="notification-overview mb-xxl">
      <div class="overview-grid">
        <MetricCard
          title="今日发送"
          :value="notificationMetrics.todaySent"
          icon="Message"
          theme="primary"
          description="今日发送通知数量"
        />
        <MetricCard
          title="发送成功率"
          :value="notificationMetrics.successRate"
          icon="CircleCheck"
          theme="success"
          suffix="%"
          description="通知发送成功率"
        />
        <MetricCard
          title="待发送"
          :value="notificationMetrics.pending"
          icon="Clock"
          theme="warning"
          description="待发送通知数量"
        />
        <MetricCard
          title="失败重试"
          :value="notificationMetrics.retrying"
          icon="RefreshRight"
          theme="info"
          description="失败重试通知数量"
        />
      </div>
    </div>

    <!-- 通知渠道状态 -->
    <el-row :gutter="16" class="mb-lg">
      <el-col :span="8">
        <el-card class="channel-card">
          <template #header>
            <div class="channel-header">
              <h3>邮件通知</h3>
              <el-switch v-model="channels.email.enabled" @change="toggleChannel('email')" />
            </div>
          </template>
          <div class="channel-stats">
            <div class="stat-item">
              <span class="stat-label">今日发送:</span>
              <span class="stat-value">{{ channels.email.todaySent }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率:</span>
              <span class="stat-value">{{ channels.email.successRate }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">状态:</span>
              <el-tag :type="channels.email.enabled ? 'success' : 'info'">
                {{ channels.email.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="channel-card">
          <template #header>
            <div class="channel-header">
              <h3>短信通知</h3>
              <el-switch v-model="channels.sms.enabled" @change="toggleChannel('sms')" />
            </div>
          </template>
          <div class="channel-stats">
            <div class="stat-item">
              <span class="stat-label">今日发送:</span>
              <span class="stat-value">{{ channels.sms.todaySent }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">成功率:</span>
              <span class="stat-value">{{ channels.sms.successRate }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">状态:</span>
              <el-tag :type="channels.sms.enabled ? 'success' : 'info'">
                {{ channels.sms.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="channel-card">
          <template #header>
            <div class="channel-header">
              <h3>站内消息</h3>
              <el-switch v-model="channels.internal.enabled" @change="toggleChannel('internal')" />
            </div>
          </template>
          <div class="channel-stats">
            <div class="stat-item">
              <span class="stat-label">今日发送:</span>
              <span class="stat-value">{{ channels.internal.todaySent }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已读率:</span>
              <span class="stat-value">{{ channels.internal.readRate }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">状态:</span>
              <el-tag :type="channels.internal.enabled ? 'success' : 'info'">
                {{ channels.internal.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 通知列表 -->
    <el-card class="notifications-card mb-lg">
      <template #header>
        <div class="card-header">
          <h3>通知记录</h3>
          <div class="header-filters">
            <el-select
              v-model="filters.status"
              placeholder="状态"
              size="small"
              clearable
              @change="applyFilters"
            >
              <el-option label="待发送" value="pending" />
              <el-option label="发送中" value="sending" />
              <el-option label="已发送" value="sent" />
              <el-option label="发送失败" value="failed" />
            </el-select>
            <el-select
              v-model="filters.channel"
              placeholder="渠道"
              size="small"
              clearable
              @change="applyFilters"
            >
              <el-option label="邮件" value="email" />
              <el-option label="短信" value="sms" />
              <el-option label="站内消息" value="internal" />
            </el-select>
            <el-input
              v-model="filters.keyword"
              placeholder="搜索标题或内容"
              size="small"
              clearable
              @input="handleSearch"
            />
          </div>
        </div>
      </template>
      
      <el-table :data="paginatedNotifications" border>
        <el-table-column prop="id" label="ID" width="120" />
        <el-table-column prop="title" label="标题" width="200" />
        <el-table-column prop="channel" label="渠道" width="100">
          <template #default="{ row }">
            <el-tag :type="getChannelType(row.channel)" size="small">
              {{ getChannelLabel(row.channel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="recipients" label="接收者" width="150">
          <template #default="{ row }">
            <el-tooltip :content="row.recipients.join(', ')" placement="top">
              <span>{{ row.recipients.length }}个接收者</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sentAt" label="发送时间" width="150">
          <template #default="{ row }">
            {{ row.sentAt ? formatDateTime(row.sentAt) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="readCount" label="已读/总数" width="120">
          <template #default="{ row }">
            {{ row.readCount }}/{{ row.recipients.length }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              :icon="View"
              @click="viewNotification(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="row.status === 'failed'"
              size="small"
              type="warning"
              :icon="RefreshRight"
              @click="retryNotification(row)"
            >
              重试
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              size="small"
              type="danger"
              :icon="Delete"
              @click="cancelNotification(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="filteredNotifications.length"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 通知模板 -->
    <el-card class="templates-card">
      <template #header>
        <div class="card-header">
          <h3>通知模板</h3>
          <el-button type="primary" size="small" :icon="Plus" @click="createTemplate">
            新建模板
          </el-button>
        </div>
      </template>
      
      <div class="templates-grid">
        <div
          v-for="template in notificationTemplates"
          :key="template.id"
          class="template-item"
        >
          <div class="template-header">
            <h4>{{ template.name }}</h4>
            <div class="template-actions">
              <el-button size="small" :icon="Edit" @click="editTemplate(template)">
                编辑
              </el-button>
              <el-button size="small" :icon="CopyDocument" @click="copyTemplate(template)">
                复制
              </el-button>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="deleteTemplate(template)"
              >
                删除
              </el-button>
            </div>
          </div>
          <div class="template-info">
            <div class="template-type">
              <el-tag :type="getChannelType(template.channel)" size="small">
                {{ getChannelLabel(template.channel) }}
              </el-tag>
            </div>
            <div class="template-description">{{ template.description }}</div>
            <div class="template-usage">使用次数: {{ template.usageCount }}</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 发送通知对话框 -->
    <el-dialog
      v-model="sendDialogVisible"
      title="发送通知"
      width="600px"
    >
      <el-form :model="sendForm" :rules="sendRules" ref="sendFormRef" label-width="100px">
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="sendForm.title" />
        </el-form-item>
        <el-form-item label="通知渠道" prop="channels">
          <el-checkbox-group v-model="sendForm.channels">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="internal">站内消息</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="接收者" prop="recipients">
          <el-select
            v-model="sendForm.recipients"
            multiple
            filterable
            placeholder="选择接收者"
            style="width: 100%"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input
            v-model="sendForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入通知内容"
          />
        </el-form-item>
        <el-form-item label="发送时间">
          <el-radio-group v-model="sendForm.sendType">
            <el-radio label="immediate">立即发送</el-radio>
            <el-radio label="scheduled">定时发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="sendForm.sendType === 'scheduled'" label="发送时间">
          <el-date-picker
            v-model="sendForm.scheduledTime"
            type="datetime"
            placeholder="选择发送时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="sendDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="sendLoading"
          @click="executeSend"
        >
          {{ sendForm.sendType === 'immediate' ? '立即发送' : '定时发送' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 通知详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="通知详情"
      width="700px"
    >
      <div v-if="currentNotification" class="notification-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="通知ID">
            {{ currentNotification.id }}
          </el-descriptions-item>
          <el-descriptions-item label="标题">
            {{ currentNotification.title }}
          </el-descriptions-item>
          <el-descriptions-item label="渠道">
            <el-tag :type="getChannelType(currentNotification.channel)">
              {{ getChannelLabel(currentNotification.channel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentNotification.status)">
              {{ getStatusLabel(currentNotification.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentNotification.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="发送时间">
            {{ currentNotification.sentAt ? formatDateTime(currentNotification.sentAt) : '未发送' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="notification-content">
          <h4>通知内容</h4>
          <div class="content-box">
            {{ currentNotification.content }}
          </div>
        </div>
        
        <div class="recipients-list">
          <h4>接收者列表</h4>
          <el-table :data="currentNotification.recipients" border max-height="200">
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="contact" label="联系方式" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getRecipientStatusType(row.status)" size="small">
                  {{ getRecipientStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="readAt" label="阅读时间">
              <template #default="{ row }">
                {{ row.readAt ? formatDateTime(row.readAt) : '-' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Bell,
  Plus,
  Setting,
  Refresh,
  Message,
  CircleCheck,
  Clock,
  RefreshRight,
  View,
  Delete,
  Edit,
  CopyDocument
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import MetricCard from '../../components/common/MetricCard.vue';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '系统管理', path: '/system' },
  { title: '通知管理', path: '/system/notifications' }
]);

// 响应式数据
const sendDialogVisible = ref(false);
const detailVisible = ref(false);
const sendLoading = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const currentNotification = ref(null);
const sendFormRef = ref();

// 通知指标
const notificationMetrics = reactive({
  todaySent: 1247,
  successRate: 98.5,
  pending: 23,
  retrying: 5
});

// 通知渠道状态
const channels = reactive({
  email: {
    enabled: true,
    todaySent: 856,
    successRate: 98.2
  },
  sms: {
    enabled: true,
    todaySent: 234,
    successRate: 97.8
  },
  internal: {
    enabled: true,
    todaySent: 157,
    readRate: 85.6
  }
});

// 筛选器
const filters = reactive({
  status: '',
  channel: '',
  keyword: ''
});

// 发送表单
const sendForm = reactive({
  title: '',
  channels: [] as string[],
  recipients: [] as string[],
  content: '',
  sendType: 'immediate',
  scheduledTime: ''
});

// 表单验证规则
const sendRules = {
  title: [
    { required: true, message: '请输入通知标题', trigger: 'blur' }
  ],
  channels: [
    { required: true, message: '请选择通知渠道', trigger: 'change' }
  ],
  recipients: [
    { required: true, message: '请选择接收者', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入通知内容', trigger: 'blur' }
  ]
};

// 用户列表
const userList = ref([
  { id: 'user1', name: '张三' },
  { id: 'user2', name: '李四' },
  { id: 'user3', name: '王五' },
  { id: 'user4', name: '赵六' }
]);

// 通知列表
const notifications = ref([
  {
    id: 'N001',
    title: '系统维护通知',
    channel: 'email',
    recipients: ['张三', '李四', '王五'],
    status: 'sent',
    content: '系统将于今晚22:00-24:00进行维护，请提前保存工作。',
    sentAt: '2024-01-15T10:00:00Z',
    readCount: 2,
    createdAt: '2024-01-15T09:30:00Z'
  },
  {
    id: 'N002',
    title: '新功能上线通知',
    channel: 'internal',
    recipients: ['全体用户'],
    status: 'sending',
    content: '新的数据分析功能已上线，欢迎体验。',
    sentAt: null,
    readCount: 0,
    createdAt: '2024-01-15T11:00:00Z'
  }
]);

// 通知模板
const notificationTemplates = ref([
  {
    id: 'T001',
    name: '系统维护模板',
    channel: 'email',
    description: '用于系统维护通知',
    usageCount: 15,
    content: '系统将于{time}进行维护，请提前保存工作。'
  },
  {
    id: 'T002',
    name: '密码重置模板',
    channel: 'sms',
    description: '用于密码重置验证码',
    usageCount: 89,
    content: '您的验证码是{code}，5分钟内有效。'
  }
]);

// 计算属性
const filteredNotifications = computed(() => {
  let result = notifications.value;
  
  if (filters.status) {
    result = result.filter(n => n.status === filters.status);
  }
  
  if (filters.channel) {
    result = result.filter(n => n.channel === filters.channel);
  }
  
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase();
    result = result.filter(n =>
      n.title.toLowerCase().includes(keyword) ||
      n.content.toLowerCase().includes(keyword)
    );
  }
  
  return result;
});

const paginatedNotifications = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredNotifications.value.slice(start, end);
});

// 方法
const formatDateTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const getChannelType = (channel: string): string => {
  const typeMap: Record<string, string> = {
    'email': 'primary',
    'sms': 'success',
    'internal': 'warning'
  };
  return typeMap[channel] || 'info';
};

const getChannelLabel = (channel: string): string => {
  const labelMap: Record<string, string> = {
    'email': '邮件',
    'sms': '短信',
    'internal': '站内消息'
  };
  return labelMap[channel] || channel;
};

const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'sending': 'warning',
    'sent': 'success',
    'failed': 'danger'
  };
  return typeMap[status] || 'info';
};

const getStatusLabel = (status: string): string => {
  const labelMap: Record<string, string> = {
    'pending': '待发送',
    'sending': '发送中',
    'sent': '已发送',
    'failed': '发送失败'
  };
  return labelMap[status] || status;
};

const getRecipientStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'sent': 'success',
    'read': 'primary',
    'failed': 'danger'
  };
  return typeMap[status] || 'info';
};

const getRecipientStatusLabel = (status: string): string => {
  const labelMap: Record<string, string> = {
    'sent': '已发送',
    'read': '已阅读',
    'failed': '发送失败'
  };
  return labelMap[status] || status;
};

const toggleChannel = (channel: string) => {
  ElMessage.success(`${getChannelLabel(channel)}渠道已${channels[channel as keyof typeof channels].enabled ? '启用' : '禁用'}`);
};

const applyFilters = () => {
  currentPage.value = 1;
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

const createNotification = () => {
  sendDialogVisible.value = true;
};

const showTemplateManager = () => {
  ElMessageBox.alert(
    '模板管理功能包括：\n• 创建通知模板\n• 编辑模板内容\n• 设置模板变量\n• 模板分类管理',
    '模板管理',
    {
      confirmButtonText: '了解',
      type: 'info'
    }
  );
};

const refreshData = () => {
  ElMessage.success('数据已刷新');
};

const viewNotification = (notification: any) => {
  currentNotification.value = {
    ...notification,
    recipients: [
      { name: '张三', contact: '<EMAIL>', status: 'read', readAt: '2024-01-15T10:30:00Z' },
      { name: '李四', contact: '<EMAIL>', status: 'sent', readAt: null },
      { name: '王五', contact: '<EMAIL>', status: 'failed', readAt: null }
    ]
  };
  detailVisible.value = true;
};

const retryNotification = async (notification: any) => {
  try {
    await ElMessageBox.confirm('确定要重试发送这条通知吗？', '确认重试', {
      type: 'warning'
    });
    
    notification.status = 'sending';
    ElMessage.success('通知重试发送中');
  } catch (error) {
    // 用户取消操作
  }
};

const cancelNotification = async (notification: any) => {
  try {
    await ElMessageBox.confirm('确定要取消这条通知吗？', '确认取消', {
      type: 'warning'
    });
    
    const index = notifications.value.findIndex(n => n.id === notification.id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
    
    ElMessage.success('通知已取消');
  } catch (error) {
    // 用户取消操作
  }
};

const executeSend = async () => {
  try {
    await sendFormRef.value.validate();
    
    sendLoading.value = true;
    
    // 模拟发送过程
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const newNotification = {
      id: `N${String(notifications.value.length + 1).padStart(3, '0')}`,
      title: sendForm.title,
      channel: sendForm.channels[0], // 简化处理，取第一个渠道
      recipients: sendForm.recipients.map(id => userList.value.find(u => u.id === id)?.name || id),
      status: sendForm.sendType === 'immediate' ? 'sending' : 'pending',
      content: sendForm.content,
      sentAt: sendForm.sendType === 'immediate' ? new Date().toISOString() : null,
      readCount: 0,
      createdAt: new Date().toISOString()
    };
    
    notifications.value.unshift(newNotification);
    
    ElMessage.success(sendForm.sendType === 'immediate' ? '通知发送成功' : '定时通知已创建');
    sendDialogVisible.value = false;
    
    // 重置表单
    Object.assign(sendForm, {
      title: '',
      channels: [],
      recipients: [],
      content: '',
      sendType: 'immediate',
      scheduledTime: ''
    });
  } catch (error) {
    // 表单验证失败
  } finally {
    sendLoading.value = false;
  }
};

const createTemplate = () => {
  ElMessageBox.prompt('请输入模板名称', '创建通知模板', {
    confirmButtonText: '创建',
    cancelButtonText: '取消',
    inputPattern: /\S+/,
    inputErrorMessage: '模板名称不能为空'
  }).then(({ value }) => {
    ElMessage.success(`模板 "${value}" 创建成功`);
  }).catch(() => {
    // 用户取消
  });
};

const editTemplate = (template: any) => {
  ElMessage.info(`编辑模板: ${template.name}`);
};

const copyTemplate = (template: any) => {
  ElMessage.success(`模板 ${template.name} 已复制`);
};

const deleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板 ${template.name} 吗？`, '确认删除', {
      type: 'danger'
    });
    
    const index = notificationTemplates.value.findIndex(t => t.id === template.id);
    if (index > -1) {
      notificationTemplates.value.splice(index, 1);
    }
    
    ElMessage.success('模板删除成功');
  } catch (error) {
    // 用户取消操作
  }
};

// 生命周期
onMounted(() => {
  console.log('通知管理页面已加载');
});
</script>

<style scoped>
/* 通知概览 */
.notification-overview {
  margin-bottom: var(--spacing-xxl);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

/* 渠道卡片 */
.channel-card {
  background: var(--bg-white);
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-header h3 {
  margin: 0;
}

.channel-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.stat-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-filters {
  display: flex;
  gap: var(--spacing-md);
}

/* 通知列表 */
.notifications-card {
  background: var(--bg-white);
}

.pagination-wrapper {
  margin-top: var(--spacing-lg);
  text-align: center;
}

/* 模板网格 */
.templates-card {
  background: var(--bg-white);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.template-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  transition: all 0.3s ease;
}

.template-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.template-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.template-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.template-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.template-description {
  color: var(--text-regular);
  font-size: var(--font-size-sm);
}

.template-usage {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

/* 通知详情 */
.notification-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.notification-content,
.recipients-list {
  margin-top: var(--spacing-lg);
}

.notification-content h4,
.recipients-list h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.content-box {
  background: var(--bg-lighter);
  border-radius: var(--radius-small);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  color: var(--text-regular);
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .header-filters {
    justify-content: space-between;
  }

  .channel-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .template-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .template-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.template-item,
.stat-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
