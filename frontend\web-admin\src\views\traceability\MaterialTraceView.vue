<template>
  <PageContainer
    title="原料溯源系统"
    description="中药材全链条溯源管理与追踪系统"
    :breadcrumb="breadcrumb"
    icon="Position"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建溯源记录
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出数据
      </el-button>
      <el-button :icon="Search" @click="handleBatchQuery">
        批量查询
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="溯源记录总数"
          :value="stats.total"
          icon="Position"
          theme="primary"
          description="系统中的溯源记录总数"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="已完成溯源"
          :value="stats.completed"
          icon="CircleCheck"
          theme="success"
          description="已完成全链条溯源的记录"
          :clickable="true"
          @click="filterByStatus('completed')"
        />
        <StatCard
          title="运输中"
          :value="stats.transporting"
          icon="Truck"
          theme="warning"
          description="正在运输中的批次"
          :clickable="true"
          @click="filterByStatus('transporting')"
        />
        <StatCard
          title="质量合格率"
          :value="stats.qualityRate"
          icon="Medal"
          theme="info"
          description="质量检测合格率"
          suffix="%"
          :clickable="true"
          @click="showQualityAnalysis"
        />
      </div>
    </div>

    <!-- 溯源状态导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>溯源状态</h3>
        <div class="category-tabs">
          <el-button
            v-for="status in traceabilityStatuses"
            :key="status.value"
            :type="selectedStatus === status.value ? 'primary' : 'default'"
            @click="selectStatus(status.value)"
          >
            {{ status.label }}
          </el-button>
          <el-button
            :type="selectedStatus === '' ? 'primary' : 'default'"
            @click="selectStatus('')"
          >
            全部状态
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-input
            v-model="filters.materialName"
            placeholder="原料名称"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.batchNumber"
            placeholder="批次号"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.traceabilityCode"
            placeholder="溯源码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="原料溯源记录列表"
      description="查看和管理所有原料溯源记录"
      :show-selection="true"
      :show-index="true"
      :search-fields="['materialName', 'batchNumber', 'traceabilityCode']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 溯源状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 种植基地列插槽 -->
      <template #plantingBase="{ row }">
        <div class="base-info">
          <div class="base-name">{{ row.plantingBase.baseName }}</div>
          <div class="base-location">{{ row.plantingBase.location.province }} {{ row.plantingBase.location.city }}</div>
        </div>
      </template>

      <!-- 质量等级列插槽 -->
      <template #qualityGrade="{ row }">
        <el-tag :type="getQualityGradeType(row.harvestInfo.qualityInspection.grade)">
          {{ row.harvestInfo.qualityInspection.grade }}
        </el-tag>
      </template>

      <!-- 认证信息列插槽 -->
      <template #certifications="{ row }">
        <div class="certifications">
          <el-tag
            v-for="cert in row.plantingBase.certifications"
            :key="cert"
            size="small"
            class="mr-xs"
          >
            {{ cert }}
          </el-tag>
        </div>
      </template>

      <!-- 溯源进度列插槽 -->
      <template #progress="{ row }">
        <div class="progress-info">
          <el-progress
            :percentage="getTraceabilityProgress(row)"
            :stroke-width="6"
            :show-text="false"
          />
          <div class="progress-text">{{ getProgressText(row) }}</div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="info"
          size="small"
          :icon="Position"
          @click="handleTrace(row)"
        >
          溯源
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="QrCode"
          @click="handleQRCode(row)"
        >
          二维码
        </el-button>
      </template>
    </DataTable>

    <!-- 溯源详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="溯源详情"
      width="1400px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="溯源码">
              {{ currentRow.traceabilityCode }}
            </el-descriptions-item>
            <el-descriptions-item label="原料名称">
              {{ currentRow.materialName }}
            </el-descriptions-item>
            <el-descriptions-item label="批次号">
              {{ currentRow.batchNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="总数量">
              {{ currentRow.totalQuantity }}{{ currentRow.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="质量等级">
              <el-tag :type="getQualityGradeType(currentRow.harvestInfo.qualityInspection.grade)">
                {{ currentRow.harvestInfo.qualityInspection.grade }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ formatDateTime(currentRow.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 种植基地信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>种植基地信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="基地名称">
              {{ currentRow.plantingBase.baseName }}
            </el-descriptions-item>
            <el-descriptions-item label="基地编码">
              {{ currentRow.plantingBase.baseCode }}
            </el-descriptions-item>
            <el-descriptions-item label="种植面积">
              {{ currentRow.plantingBase.area }}亩
            </el-descriptions-item>
            <el-descriptions-item label="基地位置" :span="3">
              {{ currentRow.plantingBase.location.province }} {{ currentRow.plantingBase.location.city }} {{ currentRow.plantingBase.location.county }} {{ currentRow.plantingBase.location.address }}
            </el-descriptions-item>
            <el-descriptions-item label="管理员">
              {{ currentRow.plantingBase.manager.managerName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ currentRow.plantingBase.manager.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="认证信息">
              <el-tag
                v-for="cert in currentRow.plantingBase.certifications"
                :key="cert"
                size="small"
                class="mr-xs"
              >
                {{ cert }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 溯源时间线 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>溯源时间线</h3>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="event in currentRow.timeline"
              :key="event.eventId"
              :timestamp="formatDateTime(event.eventTime)"
              :type="getEventType(event.eventType)"
            >
              <div class="timeline-content">
                <h4>{{ event.eventName }}</h4>
                <p>{{ event.description }}</p>
                <div class="timeline-meta">
                  <span>操作人：{{ event.operator }}</span>
                  <span>地点：{{ event.location }}</span>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 质量检测结果 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>质量检测结果</h3>
          </template>
          <el-table :data="currentRow.qualityTests" border>
            <el-table-column prop="testType" label="检测类型" width="120" />
            <el-table-column prop="testDate" label="检测日期" width="120" />
            <el-table-column prop="testInstitution" label="检测机构" width="200" />
            <el-table-column prop="tester" label="检测员" width="120" />
            <el-table-column prop="conclusion" label="检测结论" width="150">
              <template #default="{ row }">
                <el-tag :type="row.conclusion === '合格' ? 'success' : 'danger'">
                  {{ row.conclusion }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="reportNumber" label="报告编号" width="150" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewTestReport(row)"
                >
                  查看报告
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Position,
  Plus,
  Download,
  Search,
  View,
  QrCode,
  CircleCheck,
  Truck,
  Medal
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getMaterialTraceability,
  traceabilityStatuses,
  qualityGrades,
  type MaterialTraceability
} from '../../mock/material-traceability';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '供应链溯源', path: '/traceability' },
  { title: '原料溯源系统', path: '/traceability/materials' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<MaterialTraceability | null>(null);
const tableRef = ref();
const selectedStatus = ref('');

// 表格数据
const tableData = ref<MaterialTraceability[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  status: '',
  materialName: '',
  batchNumber: '',
  traceabilityCode: '',
  dateRange: null as string[] | null
});

// 统计数据
const stats = computed(() => {
  const traces = tableData.value;
  const totalCount = traces.length;
  const completedCount = traces.filter(t => t.status === 'completed').length;
  const transportingCount = traces.filter(t => t.status === 'transporting').length;
  const qualifiedCount = traces.filter(t =>
    t.qualityTests.every(test => test.conclusion === '合格')
  ).length;

  return {
    total: totalCount,
    completed: completedCount,
    transporting: transportingCount,
    qualityRate: totalCount > 0 ? Math.round((qualifiedCount / totalCount) * 100) : 0
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'traceabilityCode',
    label: '溯源码',
    width: 180,
    fixed: 'left'
  },
  {
    prop: 'materialName',
    label: '原料名称',
    width: 120
  },
  {
    prop: 'batchNumber',
    label: '批次号',
    width: 150
  },
  {
    prop: 'totalQuantity',
    label: '数量',
    width: 100,
    align: 'right',
    formatter: (row: MaterialTraceability) => `${row.totalQuantity}${row.unit}`
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'plantingBase',
    label: '种植基地',
    width: 200,
    slot: 'plantingBase'
  },
  {
    prop: 'qualityGrade',
    label: '质量等级',
    width: 100,
    slot: 'qualityGrade'
  },
  {
    prop: 'certifications',
    label: '认证信息',
    width: 150,
    slot: 'certifications'
  },
  {
    prop: 'progress',
    label: '溯源进度',
    width: 120,
    slot: 'progress'
  },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 120,
    formatter: (row: MaterialTraceability) => formatDateTime(row.createdAt)
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getMaterialTraceability({
      status: filters.status || selectedStatus.value,
      materialName: filters.materialName,
      batchNumber: filters.batchNumber,
      keyword: filters.traceabilityCode
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.status = '';
  filters.materialName = '';
  filters.batchNumber = '';
  filters.traceabilityCode = '';
  filters.dateRange = null;
  selectedStatus.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  selectedStatus.value = status;
  loadData();
};

const selectStatus = (status: string) => {
  selectedStatus.value = status;
  filters.status = status;
  loadData();
};

const getStatusLabel = (status: string) => {
  const item = traceabilityStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = traceabilityStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getQualityGradeType = (grade: string) => {
  const item = qualityGrades.find(g => g.label === grade);
  return item ? item.type : 'info';
};

const getTraceabilityProgress = (row: MaterialTraceability) => {
  const statusProgress = {
    'planting': 20,
    'harvesting': 40,
    'processing': 60,
    'transporting': 80,
    'storing': 90,
    'completed': 100,
    'recalled': 100
  };
  return statusProgress[row.status as keyof typeof statusProgress] || 0;
};

const getProgressText = (row: MaterialTraceability) => {
  return getStatusLabel(row.status);
};

const getEventType = (eventType: string) => {
  const typeMap = {
    'planting': 'primary',
    'harvesting': 'success',
    'processing': 'warning',
    'transport': 'info',
    'storage': 'success'
  };
  return typeMap[eventType as keyof typeof typeMap] || 'primary';
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const handleAdd = () => {
  ElMessage.success('新建溯源记录对话框已打开');
  // 这里可以添加打开新建对话框的逻辑
};

const handleExport = () => {
  ElMessage.success('溯源数据导出已开始');
  // 这里可以添加实际的导出逻辑
};

const handleBatchQuery = () => {
  ElMessage.success('批量查询功能已启动');
  // 这里可以添加批量查询的逻辑
};

const showQualityAnalysis = () => {
  ElMessage.info('质量分析功能开发中...');
};

const handleView = (row: MaterialTraceability) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleTrace = (row: MaterialTraceability) => {
  ElMessage.info(`查看溯源链路: ${row.traceabilityCode}`);
};

const handleQRCode = (row: MaterialTraceability) => {
  ElMessage.info(`生成二维码: ${row.traceabilityCode}`);
};

const handleEdit = (row: MaterialTraceability) => {
  ElMessage.info(`编辑溯源记录: ${row.traceabilityCode}`);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const handleSelectionChange = (selection: MaterialTraceability[]) => {
  console.log('选中的记录:', selection);
};

const handleRowClick = (row: MaterialTraceability) => {
  console.log('点击的记录:', row);
};

const viewTestReport = (test: any) => {
  ElMessage.info(`查看检测报告: ${test.reportNumber}`);
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计面板 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-input,
.filter-item .el-date-picker {
  width: 100%;
}

/* 基地信息样式 */
.base-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.base-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.base-location {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 认证信息样式 */
.certifications {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

/* 进度信息样式 */
.progress-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 详情页面样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

/* 时间线样式 */
.timeline-content h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.timeline-content p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-regular);
}

.timeline-meta {
  display: flex;
  gap: var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.base-info,
.progress-info,
.timeline-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
