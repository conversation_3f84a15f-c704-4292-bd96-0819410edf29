/**
 * 用户角色常量定义 (User Role Constants)
 */

// 角色类型枚举
export enum UserRole {
  ADMIN = 'admin',
  PLANTING_MANAGER = 'planting_manager',
  PRODUCTION_SUPERVISOR = 'production_supervisor',
  QUALITY_CONTROLLER = 'quality_controller',
  FORMULA_RESEARCHER = 'formula_researcher',
  SALES_MANAGER = 'sales_manager',
  CONSUMER = 'consumer'
}

// 角色名称映射
export const ROLE_NAMES: Record<UserRole, string> = {
  [UserRole.ADMIN]: '系统管理员',
  [UserRole.PLANTING_MANAGER]: '种植基地管理员',
  [UserRole.PRODUCTION_SUPERVISOR]: '生产车间主管',
  [UserRole.QUALITY_CONTROLLER]: '品控人员',
  [UserRole.FORMULA_RESEARCHER]: '配方师/研发',
  [UserRole.SALES_MANAGER]: '销售与市场',
  [UserRole.CONSUMER]: '消费者'
};

// 角色描述
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  [UserRole.ADMIN]: '负责系统管理和配置',
  [UserRole.PLANTING_MANAGER]: '负责原料种植与采收管理',
  [UserRole.PRODUCTION_SUPERVISOR]: '负责原料加工与成品生产',
  [UserRole.QUALITY_CONTROLLER]: '负责检测与追溯管理',
  [UserRole.FORMULA_RESEARCHER]: '负责产品开发与配方设计',
  [UserRole.SALES_MANAGER]: '负责渠道管理与推广',
  [UserRole.CONSUMER]: '产品购买与健康管理'
};

// 角色权限映射
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  [UserRole.ADMIN]: [
    'system:manage',
    'user:manage',
    'materials:view',
    'materials:edit',
    'formulas:view',
    'formulas:edit',
    'traceability:view',
    'production:view',
    'production:edit',
    'demo:view'
  ],
  [UserRole.PLANTING_MANAGER]: [
    'materials:view',
    'traceability:view'
  ],
  [UserRole.PRODUCTION_SUPERVISOR]: [
    'production:view',
    'production:edit',
    'traceability:view',
    'materials:view'
  ],
  [UserRole.QUALITY_CONTROLLER]: [
    'traceability:view',
    'materials:view',
    'materials:edit',
    'production:view'
  ],
  [UserRole.FORMULA_RESEARCHER]: [
    'formulas:view',
    'formulas:edit',
    'materials:view',
    'production:view'
  ],
  [UserRole.SALES_MANAGER]: [
    'materials:view',
    'formulas:view',
    'traceability:view',
    'production:view'
  ],
  [UserRole.CONSUMER]: [
    'traceability:view'
  ]
};

// 角色颜色主题
export const ROLE_COLORS: Record<UserRole, string> = {
  [UserRole.ADMIN]: '#409EFF',
  [UserRole.PLANTING_MANAGER]: '#67C23A',
  [UserRole.PRODUCTION_SUPERVISOR]: '#E6A23C',
  [UserRole.QUALITY_CONTROLLER]: '#F56C6C',
  [UserRole.FORMULA_RESEARCHER]: '#909399',
  [UserRole.SALES_MANAGER]: '#9C27B0',
  [UserRole.CONSUMER]: '#00BCD4'
};

// 获取角色名称
export const getRoleName = (role: UserRole): string => {
  return ROLE_NAMES[role] || role;
};

// 获取角色描述
export const getRoleDescription = (role: UserRole): string => {
  return ROLE_DESCRIPTIONS[role] || '';
};

// 获取角色权限
export const getRolePermissions = (role: UserRole): string[] => {
  return ROLE_PERMISSIONS[role] || [];
};

// 获取角色颜色
export const getRoleColor = (role: UserRole): string => {
  return ROLE_COLORS[role] || '#909399';
};

// 检查角色是否有特定权限
export const hasPermission = (role: UserRole, permission: string): boolean => {
  const permissions = getRolePermissions(role);
  return permissions.includes(permission);
};

// 检查角色是否有任意一个权限
export const hasAnyPermission = (role: UserRole, permissions: string[]): boolean => {
  const rolePermissions = getRolePermissions(role);
  return permissions.some(permission => rolePermissions.includes(permission));
};

// 检查角色是否有所有权限
export const hasAllPermissions = (role: UserRole, permissions: string[]): boolean => {
  const rolePermissions = getRolePermissions(role);
  return permissions.every(permission => rolePermissions.includes(permission));
};
