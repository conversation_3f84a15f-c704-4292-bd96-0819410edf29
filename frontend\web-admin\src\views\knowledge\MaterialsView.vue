<template>
  <PageContainer
    title="原料数据库"
    description="管理和查看所有原料信息"
    :breadcrumb="breadcrumb"
    icon="Box"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        添加原料
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出数据
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="原料总数"
          :value="stats.total"
          icon="Box"
          theme="primary"
          description="系统中的原料总数量"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="正常库存"
          :value="stats.normal"
          icon="CircleCheck"
          theme="success"
          description="库存充足的原料数量"
          :clickable="true"
          @click="filterByStock('normal')"
        />
        <StatCard
          title="库存预警"
          :value="stats.warning"
          icon="Warning"
          theme="warning"
          description="库存不足需要补充"
          :clickable="true"
          @click="filterByStock('warning')"
        />
        <StatCard
          title="缺货原料"
          :value="stats.outOfStock"
          icon="Close"
          theme="danger"
          description="库存为零的原料"
          :clickable="true"
          @click="filterByStock('out')"
        />
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.category"
            placeholder="选择分类"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="category in materialCategories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in materialStatus"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索原料名称、编码或拉丁名"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="原料列表"
      description="查看和管理所有原料信息"
      :show-selection="true"
      :show-index="true"
      :search-fields="['name', 'code', 'latinName']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 分类列插槽 -->
      <template #category="{ row }">
        <el-tag type="info">
          {{ getCategoryLabel(row.category) }}
        </el-tag>
      </template>

      <!-- 库存列插槽 -->
      <template #stock="{ row }">
        <div class="stock-info">
          <span :class="getStockClass(row)">{{ row.stock }}</span>
          <span class="stock-unit">{{ row.unit }}</span>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 价格列插槽 -->
      <template #price="{ row }">
        <span class="price-text">¥{{ row.price }}/{{ row.unit }}</span>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="danger"
          size="small"
          :icon="Delete"
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 原料详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="原料详情"
      width="1400px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="原料编码">
              {{ currentRow.materialCode }}
            </el-descriptions-item>
            <el-descriptions-item label="原料名称">
              {{ currentRow.materialName }}
            </el-descriptions-item>
            <el-descriptions-item label="学名">
              {{ currentRow.scientificName }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              {{ getCategoryLabel(currentRow.category) }}
            </el-descriptions-item>
            <el-descriptions-item label="主产地">
              {{ currentRow.origin.primaryOrigin }}
            </el-descriptions-item>
            <el-descriptions-item label="采收季节">
              {{ currentRow.origin.harvestSeason }}
            </el-descriptions-item>
            <el-descriptions-item label="道地性">
              {{ currentRow.origin.geoAuthenticity ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="别名" :span="2">
              {{ currentRow.commonNames.join('、') || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ formatDateTime(currentRow.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 性状特征 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>性状特征</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="外观">
              {{ currentRow.basicInfo.appearance }}
            </el-descriptions-item>
            <el-descriptions-item label="气味">
              {{ currentRow.basicInfo.odor }}
            </el-descriptions-item>
            <el-descriptions-item label="味道">
              {{ currentRow.basicInfo.taste }}
            </el-descriptions-item>
            <el-descriptions-item label="颜色">
              {{ currentRow.basicInfo.color }}
            </el-descriptions-item>
            <el-descriptions-item label="质地">
              {{ currentRow.basicInfo.texture }}
            </el-descriptions-item>
            <el-descriptions-item label="大小">
              {{ currentRow.basicInfo.size }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 活性成分 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>活性成分</h3>
          </template>
          <el-table :data="currentRow.activeComponents" border>
            <el-table-column prop="componentName" label="成分名称" width="150" />
            <el-table-column prop="chemicalName" label="化学名称" min-width="200" />
            <el-table-column prop="molecularFormula" label="分子式" width="120" />
            <el-table-column prop="content.typicalContent" label="典型含量" width="100" align="right">
              <template #default="{ row }">
                {{ row.content.typicalContent }}{{ row.content.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="bioactivity" label="生物活性" min-width="200">
              <template #default="{ row }">
                <el-tag
                  v-for="activity in row.bioactivity"
                  :key="activity"
                  size="small"
                  class="mr-xs"
                >
                  {{ activity }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 供应商信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>供应商信息</h3>
          </template>
          <el-table :data="currentRow.suppliers" border>
            <el-table-column prop="supplierName" label="供应商名称" width="200" />
            <el-table-column prop="qualityLevel" label="质量等级" width="100">
              <template #default="{ row }">
                <el-tag :type="row.qualityLevel === 'premium' ? 'success' : 'primary'">
                  {{ row.qualityLevel === 'premium' ? '特级' : '一级' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priceInfo.currentPrice" label="当前价格" width="100" align="right">
              <template #default="{ row }">
                ¥{{ row.priceInfo.currentPrice }}/{{ row.priceInfo.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="supplyCapacity.monthlyCapacity" label="月供应量" width="120" align="right">
              <template #default="{ row }">
                {{ row.supplyCapacity.monthlyCapacity }}{{ row.supplyCapacity.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="evaluation.overallRating" label="综合评分" width="100" align="center">
              <template #default="{ row }">
                <el-rate
                  :model-value="row.evaluation.overallRating / 2"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </template>
            </el-table-column>
            <el-table-column prop="contactInfo.phone" label="联系电话" width="120" />
          </el-table>
        </el-card>

        <!-- 储存信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>储存信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="储存条件">
              {{ currentRow.storageInfo.storageConditions }}
            </el-descriptions-item>
            <el-descriptions-item label="温度要求">
              {{ currentRow.storageInfo.temperature }}
            </el-descriptions-item>
            <el-descriptions-item label="湿度要求">
              {{ currentRow.storageInfo.humidity }}
            </el-descriptions-item>
            <el-descriptions-item label="光照条件">
              {{ currentRow.storageInfo.lightConditions }}
            </el-descriptions-item>
            <el-descriptions-item label="保质期">
              {{ currentRow.storageInfo.shelfLife }}
            </el-descriptions-item>
            <el-descriptions-item label="包装方式">
              {{ currentRow.storageInfo.packaging }}
            </el-descriptions-item>
            <el-descriptions-item label="储存注意事项" :span="3">
              <el-tag
                v-for="warning in currentRow.storageInfo.storageWarnings"
                :key="warning"
                type="warning"
                size="small"
                class="mr-xs"
              >
                {{ warning }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
      </template>
    </el-dialog>

    <!-- 新增/编辑原料对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      :title="editingId ? '编辑原料' : '新增原料'"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="原料名称" prop="name">
              <el-input v-model="addForm.name" placeholder="请输入原料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学名" prop="scientificName">
              <el-input v-model="addForm.scientificName" placeholder="请输入学名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="addForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option
                  v-for="category in materialCategories"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="addForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option
                  v-for="status in materialStatuses"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产地" prop="origin">
              <el-input v-model="addForm.origin" placeholder="请输入产地" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质量等级" prop="qualityGrade">
              <el-select v-model="addForm.qualityGrade" placeholder="请选择质量等级" style="width: 100%">
                <el-option
                  v-for="grade in qualityGrades"
                  :key="grade.value"
                  :label="grade.label"
                  :value="grade.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="addForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入原料描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ editingId ? '更新' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Box,
  Plus,
  Download,
  Search,
  View,
  Edit,
  Delete,
  CircleCheck,
  Warning,
  Close
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import BaseForm from '../../components/common/BaseForm.vue';
import {
  getRawMaterials,
  getRawMaterialById,
  materialCategories,
  materialStatuses,
  qualityGrades,
  type RawMaterial
} from '../../mock/raw-materials';

// 添加材料状态常量
const materialStatus = materialStatuses;

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '数据标准与知识库', path: '/knowledge' },
  { title: '原料数据库', path: '/knowledge/materials' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const addDialogVisible = ref(false);
const saving = ref(false);
const editingId = ref('');
const currentRow = ref<RawMaterial | null>(null);
const tableRef = ref();
const selectedCategory = ref('');
const addFormRef = ref();

// 表格数据
const tableData = ref<RawMaterial[]>([]);
const total = ref(0);

// 新增表单数据
const addForm = reactive({
  name: '',
  scientificName: '',
  category: '',
  status: '',
  origin: '',
  qualityGrade: '',
  description: ''
});

// 表单验证规则
const addFormRules = {
  name: [
    { required: true, message: '请输入原料名称', trigger: 'blur' }
  ],
  scientificName: [
    { required: true, message: '请输入学名', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  origin: [
    { required: true, message: '请输入产地', trigger: 'blur' }
  ],
  qualityGrade: [
    { required: true, message: '请选择质量等级', trigger: 'change' }
  ]
};

// 筛选器
const filters = reactive({
  category: '',
  status: '',
  qualityGrade: '',
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const materials = tableData.value;
  return {
    total: materials.length,
    normal: materials.filter(m => m.status === 'active').length,
    warning: materials.filter(m => m.status === 'warning').length,
    outOfStock: materials.filter(m => m.status === 'inactive').length
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'materialCode',
    label: '编码',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'materialName',
    label: '原料名称',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'scientificName',
    label: '学名',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'category',
    label: '分类',
    width: 100,
    slot: 'category'
  },
  {
    prop: 'origin.primaryOrigin',
    label: '主产地',
    width: 120
  },
  {
    prop: 'activeComponents',
    label: '活性成分',
    width: 120,
    slot: 'activeComponents'
  },
  {
    prop: 'suppliers',
    label: '供应商数量',
    width: 100,
    slot: 'suppliers',
    align: 'center'
  },
  {
    prop: 'qualityStandards',
    label: '质量标准',
    width: 100,
    slot: 'qualityStandards',
    align: 'center'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  }
]);





// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getRawMaterials({
      category: filters.category || selectedCategory.value,
      status: filters.status,
      qualityGrade: filters.qualityGrade,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.category = '';
  filters.status = '';
  filters.qualityGrade = '';
  filters.keyword = '';
  selectedCategory.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const filterByStock = (stockType: string) => {
  // 根据库存类型设置筛选条件
  switch (stockType) {
    case 'normal':
      filters.status = 'active';
      break;
    case 'warning':
      filters.status = 'warning';
      break;
    case 'out':
      filters.status = 'inactive';
      break;
    default:
      filters.status = '';
  }
  loadData();
};

const selectCategory = (category: string) => {
  selectedCategory.value = category;
  filters.category = category;
  loadData();
};

const getCategoryLabel = (category: string) => {
  const item = materialCategories.find(c => c.value === category);
  return item ? item.label : category;
};

const getStatusLabel = (status: string) => {
  const item = materialStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = materialStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const showStandardsAnalysis = () => {
  ElMessage.success('质量标准分析报告已生成');
  // 这里可以添加实际的分析逻辑
};

const showSuppliersAnalysis = () => {
  ElMessage.success('供应商分析报告已生成');
  // 这里可以添加实际的分析逻辑
};

const getStockClass = (row: any) => {
  if (row.stock === 0) return 'stock-out';
  if (row.stock <= row.minStock) return 'stock-warning';
  return 'stock-normal';
};

const getStockStatusType = (row: any) => {
  if (row.stock === 0) return 'danger';
  if (row.stock <= row.minStock) return 'warning';
  return 'success';
};

const getStockStatusText = (row: any) => {
  if (row.stock === 0) return '缺货';
  if (row.stock <= row.minStock) return '库存不足';
  return '库存充足';
};

const getComponentType = (type: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    nutrition: 'warning',
    functional: 'info'
  };
  return typeMap[type] || 'info';
};

const getComponentLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    active: '活性成分',
    nutrition: '营养成分',
    functional: '功能成分'
  };
  return labelMap[type] || type;
};

const getQualityTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    appearance: '性状',
    physical: '理化',
    microbial: '微生物',
    heavy_metal: '重金属',
    pesticide: '农残'
  };
  return labelMap[type] || type;
};

const handleAdd = () => {
  editingId.value = '';
  Object.assign(addForm, {
    name: '',
    scientificName: '',
    category: '',
    status: '',
    origin: '',
    qualityGrade: '',
    description: ''
  });
  addDialogVisible.value = true;
};

const handleEdit = (row: RawMaterial) => {
  ElMessage.info(`编辑原料: ${row.materialName}`);
};

const handleView = (row: RawMaterial) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleDelete = async (row: RawMaterial) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除原料 "${row.materialName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    ElMessage.success('删除成功');
    loadData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleExport = () => {
  ElMessage.success('数据导出已开始，请稍候下载');
  // 这里可以添加实际的导出逻辑
};

const handleSave = async () => {
  if (!addFormRef.value) return;

  try {
    await addFormRef.value.validate();
    saving.value = true;

    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success(editingId.value ? '原料更新成功' : '原料添加成功');
    addDialogVisible.value = false;
    loadData(); // 重新加载数据
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    saving.value = false;
  }
};

const handleSelectionChange = (selection: RawMaterial[]) => {
  console.log('选中的原料:', selection);
};

const handleRowClick = (row: RawMaterial) => {
  console.log('点击的原料:', row);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input {
  width: 100%;
}

/* 库存信息样式 */
.stock-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stock-normal {
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

.stock-warning {
  color: var(--warning-color);
  font-weight: var(--font-weight-medium);
}

.stock-out {
  color: var(--danger-color);
  font-weight: var(--font-weight-medium);
}

.stock-unit {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 价格样式 */
.price-text {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .stock-detail {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 表格内容样式 */
:deep(.el-table) {
  .stock-info {
    justify-content: flex-end;
  }

  .price-text {
    display: block;
    text-align: right;
  }
}

/* 对话框样式覆盖 */
:deep(.el-dialog) {
  .el-dialog__body {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
}

/* 表单样式 */
:deep(.el-form) {
  .el-form-item__label {
    font-weight: var(--font-weight-medium);
  }
}

/* 动画效果 */
.detail-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
