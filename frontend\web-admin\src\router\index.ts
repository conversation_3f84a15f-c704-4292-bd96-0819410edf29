/**
 * 路由配置 (Router Configuration)
 */

import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { ElMessage } from 'element-plus';

// 路由定义 (Route Definitions)
const routes: RouteRecordRaw[] = [
  // 根路径重定向到仪表盘
  {
    path: '/',
    redirect: '/dashboard'
  },

  // 认证相关路由 (Authentication Routes)
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/LoginView.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },

  // 主应用路由 (Main Application Routes)
  {
    path: '/dashboard',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/dashboard/home',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'home',
        name: 'Dashboard',
        component: () => import('../views/dashboard/DashboardView.vue'),
        meta: {
          title: '仪表盘',
          requiresAuth: true,
          icon: 'Dashboard'
        }
      }
    ]
  },

  // 数据标准与知识库管理 (Data Standards & Knowledge Base)
  {
    path: '/knowledge',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/knowledge/materials',
    meta: {
      title: '数据标准与知识库',
      requiresAuth: true,
      icon: 'Collection',
      permissions: ['materials:view', 'formulas:view']
    },
    children: [
      {
        path: 'materials',
        name: 'Materials',
        component: () => import('../views/knowledge/MaterialsView.vue'),
        meta: {
          title: '原料数据库',
          requiresAuth: true,
          permissions: ['materials:view']
        }
      },
      {
        path: 'formulas',
        name: 'Formulas',
        component: () => import('../views/knowledge/FormulasView.vue'),
        meta: {
          title: '配方数据库',
          requiresAuth: true,
          permissions: ['formulas:view']
        }
      },
      {
        path: 'efficacy',
        name: 'Efficacy',
        component: () => import('../views/knowledge/EfficacyView.vue'),
        meta: {
          title: '功效物质验证',
          requiresAuth: true,
          permissions: ['formulas:view']
        }
      }
    ]
  },

  // 供应链追溯与品控 (Supply Chain Traceability)
  {
    path: '/traceability',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/traceability/materials',
    meta: {
      title: '供应链追溯与品控',
      requiresAuth: true,
      icon: 'Link',
      permissions: ['traceability:view']
    },
    children: [
      {
        path: 'materials',
        name: 'MaterialTrace',
        component: () => import('../views/traceability/MaterialTraceView.vue'),
        meta: {
          title: '原料溯源',
          requiresAuth: true,
          permissions: ['traceability:view']
        }
      },
      {
        path: 'production',
        name: 'ProductionTrace',
        component: () => import('../views/traceability/ProductionTraceView.vue'),
        meta: {
          title: '生产追溯',
          requiresAuth: true,
          permissions: ['traceability:view', 'production:view']
        }
      },
      {
        path: 'iot',
        name: 'IoTData',
        component: () => import('../views/traceability/IoTDataView.vue'),
        meta: {
          title: 'IoT数据',
          requiresAuth: true,
          permissions: ['traceability:view']
        }
      }
    ]
  },

  // 生产与运营管理 (Production & Operations)
  {
    path: '/production',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/production/planning',
    meta: {
      title: '生产与运营管理',
      requiresAuth: true,
      icon: 'Setting',
      permissions: ['production:view']
    },
    children: [
      {
        path: 'planning',
        name: 'ProductionPlanning',
        component: () => import('../views/production/PlanningView.vue'),
        meta: {
          title: '生产计划',
          requiresAuth: true,
          permissions: ['production:view']
        }
      },
      {
        path: 'mes',
        name: 'MESIntegration',
        component: () => import('../views/production/MESView.vue'),
        meta: {
          title: 'MES系统',
          requiresAuth: true,
          permissions: ['production:view']
        }
      },
      {
        path: 'bom',
        name: 'BOMManagement',
        component: () => import('../views/production/BOMView.vue'),
        meta: {
          title: '工艺BOM',
          requiresAuth: true,
          permissions: ['production:view', 'formulas:view']
        }
      },
      {
        path: 'alerts',
        name: 'ProductionAlerts',
        component: () => import('../views/production/AlertsView.vue'),
        meta: {
          title: '生产预警',
          requiresAuth: true,
          permissions: ['production:view']
        }
      }
    ]
  },

  // 智能客户服务与营销 (Customer Service & Marketing)
  {
    path: '/customer',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/customer/ai-advisor',
    meta: {
      title: '智能客户服务',
      requiresAuth: true,
      icon: 'User',
      permissions: []
    },
    children: [
      {
        path: 'ai-advisor',
        name: 'AIAdvisor',
        component: () => import('../views/customer/AIAdvisorView.vue'),
        meta: {
          title: 'AI养生顾问',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'health-profile',
        name: 'HealthProfile',
        component: () => import('../views/customer/HealthProfileView.vue'),
        meta: {
          title: '健康档案管理',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 精准营养与健康管理 (Precision Nutrition & Health Management)
  {
    path: '/nutrition',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/nutrition/health-data',
    meta: {
      title: '精准营养管理',
      requiresAuth: true,
      icon: 'DataAnalysis',
      permissions: []
    },
    children: [
      {
        path: 'health-data',
        name: 'HealthData',
        component: () => import('../views/nutrition/HealthDataView.vue'),
        meta: {
          title: '健康数据采集',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 质量管理 (Quality Management)
  {
    path: '/quality',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/quality/batch-testing',
    meta: {
      title: '质量管理',
      requiresAuth: true,
      icon: 'CircleCheck',
      permissions: []
    },
    children: [
      {
        path: 'batch-testing',
        name: 'BatchTesting',
        component: () => import('../views/quality/BatchTestingView.vue'),
        meta: {
          title: '批次检测',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'certificates',
        name: 'Certificates',
        component: () => import('../views/quality/CertificatesView.vue'),
        meta: {
          title: '证书管理',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'protocols',
        name: 'Protocols',
        component: () => import('../views/quality/ProtocolsView.vue'),
        meta: {
          title: '检测协议',
          requiresAuth: true,
          permissions: []
        }
      },
      {
        path: 'standards',
        name: 'Standards',
        component: () => import('../views/quality/StandardsView.vue'),
        meta: {
          title: '质量标准',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 数据安全与合规 (Data Security & Compliance)
  {
    path: '/security',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/security/data-security',
    meta: {
      title: '数据安全与合规',
      requiresAuth: true,
      icon: 'Lock',
      permissions: []
    },
    children: [
      {
        path: 'data-security',
        name: 'DataSecurity',
        component: () => import('../views/security/DataSecurityView.vue'),
        meta: {
          title: '数据安全管理',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 培训与知识管理 (Training & Knowledge Management)
  {
    path: '/training',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/training/online-training',
    meta: {
      title: '培训与知识管理',
      requiresAuth: true,
      icon: 'Document',
      permissions: []
    },
    children: [
      {
        path: 'online-training',
        name: 'OnlineTraining',
        component: () => import('../views/training/OnlineTrainingView.vue'),
        meta: {
          title: '在线培训系统',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 数据分析与决策支持 (Data Analysis & Decision Support)
  {
    path: '/analytics',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/analytics/production',
    meta: {
      title: '数据分析与决策',
      requiresAuth: true,
      icon: 'TrendCharts',
      permissions: []
    },
    children: [
      {
        path: 'production',
        name: 'ProductionAnalytics',
        component: () => import('../views/analytics/ProductionAnalyticsView.vue'),
        meta: {
          title: '生产数据分析',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 演示管理 (Demo Management)
  {
    path: '/demo',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/demo/control',
    meta: {
      title: '演示管理',
      requiresAuth: true,
      icon: 'Monitor',
      permissions: []
    },
    children: [
      {
        path: 'control',
        name: 'DemoControl',
        component: () => import('../views/demo/DemoControlPanel.vue'),
        meta: {
          title: '演示控制面板',
          requiresAuth: true,
          permissions: []
        }
      }
    ]
  },

  // 系统管理 (System Management)
  {
    path: '/system',
    component: () => import('../layouts/MainLayout.vue'),
    redirect: '/system/users',
    meta: {
      title: '系统管理',
      requiresAuth: true,
      icon: 'Setting',
      permissions: ['system:manage']
    },
    children: [
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('../views/system/UserManagementView.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          permissions: ['user:manage']
        }
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: () => import('../views/system/SystemSettingsView.vue'),
        meta: {
          title: '系统设置',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      },
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: () => import('../views/system/SystemMonitorView.vue'),
        meta: {
          title: '系统监控',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      },
      {
        path: 'api-docs',
        name: 'ApiDocumentation',
        component: () => import('../views/system/ApiDocumentationView.vue'),
        meta: {
          title: 'API文档',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      },
      {
        path: 'data',
        name: 'DataManagement',
        component: () => import('../views/system/DataManagementView.vue'),
        meta: {
          title: '数据管理',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      },
      {
        path: 'logs',
        name: 'LogManagement',
        component: () => import('../views/system/LogManagementView.vue'),
        meta: {
          title: '日志管理',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      },
      {
        path: 'notifications',
        name: 'NotificationManagement',
        component: () => import('../views/system/NotificationManagementView.vue'),
        meta: {
          title: '通知管理',
          requiresAuth: true,
          permissions: ['system:manage']
        }
      }
    ]
  },

  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/error/NotFoundView.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  }
];

// 创建路由实例 (Create Router Instance)
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 路由守卫 (Route Guards)
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 药食同源数字化管理系统`;
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 如果未登录，重定向到登录页
    if (!authStore.isAuthenticated) {
      ElMessage.warning('请先登录');
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
      return;
    }

    // 检查权限
    if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
      const hasPermission = authStore.hasAnyPermission(to.meta.permissions as any[]);
      if (!hasPermission) {
        ElMessage.error('您没有访问此页面的权限');
        next('/dashboard/home');
        return;
      }
    }
  } else {
    // 如果已登录且访问登录页，重定向到仪表盘
    if (to.path === '/login' && authStore.isAuthenticated) {
      next('/dashboard/home');
      return;
    }
  }

  next();
});

export default router;
