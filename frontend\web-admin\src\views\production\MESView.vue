<template>
  <PageContainer
    title="MES制造执行系统"
    description="实时监控和管理生产制造过程"
    :breadcrumb="breadcrumb"
    icon="Monitor"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleCreateOrder">
        新建订单
      </el-button>
      <el-button :icon="Refresh" @click="loadData">
        刷新数据
      </el-button>
    </template>

    <!-- 实时监控面板 -->
    <div class="monitoring-panel mb-xxl">
      <div class="panel-grid">
        <StatCard
          title="生产订单"
          :value="stats.totalOrders"
          icon="Document"
          theme="primary"
          description="当前系统中的生产订单"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="生产中"
          :value="stats.inProgress"
          icon="PlayCircle"
          theme="success"
          description="正在生产的订单数量"
          :clickable="true"
          @click="filterByStatus('in_progress')"
        />
        <StatCard
          title="活跃设备"
          :value="stats.activeEquipment"
          icon="Setting"
          theme="warning"
          description="当前运行中的设备数量"
          :clickable="true"
          @click="showEquipmentStatus"
        />
        <StatCard
          title="在线操作员"
          :value="stats.activeOperators"
          icon="User"
          theme="info"
          description="当前在线的操作员数量"
          :clickable="true"
          @click="showOperatorStatus"
        />
      </div>
    </div>

    <!-- 实时警报 -->
    <el-card v-if="realTimeAlerts.length > 0" class="alerts-card mb-lg">
      <template #header>
        <div class="alerts-header">
          <h3>实时警报</h3>
          <el-button size="small" @click="acknowledgeAllAlerts">全部确认</el-button>
        </div>
      </template>
      <div class="alerts-list">
        <el-alert
          v-for="alert in realTimeAlerts"
          :key="alert.id"
          :title="alert.message"
          :type="getAlertType(alert.severity)"
          :closable="true"
          @close="acknowledgeAlert(alert.id)"
        >
          <template #default>
            <div class="alert-details">
              <span class="alert-source">来源: {{ alert.source }}</span>
              <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in orderStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.priority"
            placeholder="选择优先级"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="priority in priorities"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索订单号、产品名称"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 生产订单表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="生产订单列表"
      description="查看和管理所有生产订单"
      :show-selection="true"
      :show-index="true"
      :search-fields="['orderNumber', 'productInfo.productName', 'productInfo.batchNumber']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 产品信息列插槽 -->
      <template #productInfo="{ row }">
        <div class="product-info">
          <div class="product-name">{{ row.productInfo.productName }}</div>
          <div class="batch-number">批次：{{ row.productInfo.batchNumber }}</div>
        </div>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 优先级列插槽 -->
      <template #priority="{ row }">
        <el-tag :type="getPriorityType(row.priority)">
          {{ getPriorityLabel(row.priority) }}
        </el-tag>
      </template>

      <!-- 进度列插槽 -->
      <template #progress="{ row }">
        <div class="progress-info">
          <el-progress
            :percentage="row.realTimeData.overallProgress"
            :status="getProgressStatus(row.realTimeData.overallProgress)"
            :stroke-width="8"
          />
          <div class="progress-text">
            {{ row.realTimeData.currentStep }}
          </div>
        </div>
      </template>

      <!-- 实时数据列插槽 -->
      <template #realTimeData="{ row }">
        <div class="realtime-data">
          <div class="data-item">
            <span class="data-label">操作员:</span>
            <span class="data-value">{{ row.realTimeData.activeOperators }}</span>
          </div>
          <div class="data-item">
            <span class="data-label">设备:</span>
            <span class="data-value">{{ row.realTimeData.runningEquipment }}</span>
          </div>
          <div class="data-item">
            <span class="data-label">质量:</span>
            <span class="data-value">{{ row.realTimeData.qualityScore }}%</span>
          </div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status === 'in_progress'"
          type="warning"
          size="small"
          :icon="Monitor"
          @click="handleMonitor(row)"
        >
          监控
        </el-button>
        <el-button
          v-if="row.status === 'pending'"
          type="success"
          size="small"
          :icon="VideoPlay"
          @click="handleStart(row)"
        >
          开始
        </el-button>
      </template>
    </DataTable>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="生产订单详情"
      width="1400px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="4" border>
            <el-descriptions-item label="订单编号">
              {{ currentRow.orderNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">
              {{ currentRow.productInfo.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="批次号">
              {{ currentRow.productInfo.batchNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="计划数量">
              {{ currentRow.orderDetails.plannedQuantity }} {{ currentRow.orderDetails.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="实际数量">
              {{ currentRow.orderDetails.actualQuantity }} {{ currentRow.orderDetails.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="计划开始">
              {{ formatDateTime(currentRow.orderDetails.plannedStartTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="计划结束">
              {{ formatDateTime(currentRow.orderDetails.plannedEndTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 实时数据 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>实时数据</h3>
          </template>
          <div class="realtime-dashboard">
            <div class="dashboard-item">
              <div class="dashboard-label">总体进度</div>
              <el-progress
                :percentage="currentRow.realTimeData.overallProgress"
                :stroke-width="12"
                :status="getProgressStatus(currentRow.realTimeData.overallProgress)"
              />
            </div>
            <div class="dashboard-stats">
              <div class="stat-card">
                <div class="stat-value">{{ currentRow.realTimeData.activeOperators }}</div>
                <div class="stat-label">活跃操作员</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentRow.realTimeData.runningEquipment }}</div>
                <div class="stat-label">运行设备</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentRow.realTimeData.qualityScore }}%</div>
                <div class="stat-label">质量评分</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ currentRow.realTimeData.efficiency }}%</div>
                <div class="stat-label">生产效率</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 工作站状态 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>工作站状态</h3>
          </template>
          <div class="workstations-grid">
            <div
              v-for="station in currentRow.workStations"
              :key="station.id"
              class="station-card"
              :class="getStationCardClass(station.status)"
            >
              <div class="station-header">
                <h4>{{ station.stationName }}</h4>
                <el-tag :type="getStationStatusType(station.status)" size="small">
                  {{ getStationStatusLabel(station.status) }}
                </el-tag>
              </div>
              <div class="station-content">
                <div class="station-info">
                  <span class="info-label">工序:</span>
                  <span class="info-value">{{ station.processStep }}</span>
                </div>
                <div class="station-info" v-if="station.operatorName">
                  <span class="info-label">操作员:</span>
                  <span class="info-value">{{ station.operatorName }}</span>
                </div>
                <div class="station-progress">
                  <el-progress
                    :percentage="station.progress"
                    :stroke-width="6"
                    :show-text="false"
                  />
                  <span class="progress-text">{{ station.progress }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleMonitor(currentRow)">实时监控</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Monitor,
  Plus,
  Refresh,
  Search,
  Document,
  PlayCircle,
  Setting,
  User,
  View,
  VideoPlay
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getProductionOrders,
  orderStatuses,
  stationStatuses,
  type ProductionOrder,
  type Alert
} from '../../mock/mes-system';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '生产与运营管理', path: '/production' },
  { title: 'MES系统', path: '/production/mes' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<ProductionOrder | null>(null);
const tableRef = ref();

// 表格数据
const tableData = ref<ProductionOrder[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  status: '',
  priority: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 优先级选项
const priorities = [
  { value: 'low', label: '低', type: 'info' },
  { value: 'medium', label: '中', type: 'warning' },
  { value: 'high', label: '高', type: 'danger' },
  { value: 'urgent', label: '紧急', type: 'danger' }
];

// 实时警报
const realTimeAlerts = ref<Alert[]>([
  {
    id: 'alert_001',
    type: 'quality',
    severity: 'low',
    message: '提取温度略低于设定值',
    source: '多功能提取罐',
    timestamp: '2025-01-20T15:30:00Z',
    acknowledged: false
  }
]);

// 统计数据
const stats = computed(() => {
  const orders = tableData.value;
  return {
    totalOrders: orders.length,
    inProgress: orders.filter(o => o.status === 'in_progress').length,
    activeEquipment: orders.reduce((sum, o) => sum + o.realTimeData.runningEquipment, 0),
    activeOperators: orders.reduce((sum, o) => sum + o.realTimeData.activeOperators, 0)
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'orderNumber',
    label: '订单编号',
    width: 140,
    fixed: 'left'
  },
  {
    prop: 'productInfo',
    label: '产品信息',
    width: 200,
    slot: 'productInfo'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'priority',
    label: '优先级',
    width: 80,
    slot: 'priority'
  },
  {
    prop: 'progress',
    label: '生产进度',
    width: 150,
    slot: 'progress'
  },
  {
    prop: 'realTimeData',
    label: '实时数据',
    width: 150,
    slot: 'realTimeData'
  },
  {
    prop: 'orderDetails.plannedQuantity',
    label: '计划数量',
    width: 100,
    formatter: (row: ProductionOrder) => `${row.orderDetails.plannedQuantity} ${row.orderDetails.unit}`
  },
  {
    prop: 'orderDetails.actualQuantity',
    label: '实际数量',
    width: 100,
    formatter: (row: ProductionOrder) => `${row.orderDetails.actualQuantity} ${row.orderDetails.unit}`
  },
  {
    prop: 'orderDetails.plannedStartTime',
    label: '计划开始',
    width: 120,
    formatter: (row: ProductionOrder) => formatDate(row.orderDetails.plannedStartTime)
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getProductionOrders({
      status: filters.status,
      priority: filters.priority,
      dateRange: filters.dateRange,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.status = '';
  filters.priority = '';
  filters.dateRange = null;
  filters.keyword = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const getStatusLabel = (status: string) => {
  const item = orderStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = orderStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getPriorityLabel = (priority: string) => {
  const item = priorities.find(p => p.value === priority);
  return item ? item.label : priority;
};

const getPriorityType = (priority: string) => {
  const item = priorities.find(p => p.value === priority);
  return item ? item.type : 'info';
};

const getProgressStatus = (percentage: number) => {
  if (percentage === 100) return 'success';
  if (percentage >= 80) return 'warning';
  return '';
};

const getStationStatusLabel = (status: string) => {
  const item = stationStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStationStatusType = (status: string) => {
  const item = stationStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getStationCardClass = (status: string) => {
  return {
    'station-running': status === 'running',
    'station-completed': status === 'completed',
    'station-pending': status === 'pending',
    'station-fault': status === 'fault'
  };
};

const getAlertType = (severity: string) => {
  const typeMap: Record<string, string> = {
    low: 'info',
    medium: 'warning',
    high: 'error',
    critical: 'error'
  };
  return typeMap[severity] || 'info';
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const formatDate = (timestamp: string) => {
  return new Date(timestamp).toLocaleDateString('zh-CN');
};

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

const handleCreateOrder = () => {
  // 创建新的生产订单
  const newOrder: ProductionOrder = {
    id: `order_${Date.now()}`,
    orderNumber: `PO${Date.now().toString().slice(-6)}`,
    productName: '新建产品',
    batchNumber: `B${Date.now().toString().slice(-4)}`,
    quantity: 1000,
    unit: 'kg',
    status: 'pending',
    priority: 'medium',
    plannedStartTime: new Date().toISOString(),
    plannedEndTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    actualStartTime: '',
    actualEndTime: '',
    progress: 0,
    operator: '待分配',
    equipment: '待分配',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  productionOrders.value.unshift(newOrder);
  ElMessage.success('生产订单创建成功');
};

const handleView = (row: ProductionOrder) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleMonitor = (row: ProductionOrder) => {
  ElMessage.info(`进入实时监控: ${row.orderNumber}`);
};

const handleStart = (row: ProductionOrder) => {
  ElMessage.info(`开始生产订单: ${row.orderNumber}`);
};

const handleSelectionChange = (selection: ProductionOrder[]) => {
  console.log('选中的订单:', selection);
};

const handleRowClick = (row: ProductionOrder) => {
  console.log('点击的订单:', row);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

const showEquipmentStatus = () => {
  ElNotification({
    title: '设备状态监控',
    message: '设备运行正常，温度: 25°C，湿度: 60%，压力: 1.2MPa',
    type: 'success',
    duration: 4000
  });
};

const showOperatorStatus = () => {
  ElNotification({
    title: '操作员状态',
    message: '当前在线操作员: 8人，今日工作时长: 6.5小时',
    type: 'info',
    duration: 4000
  });
};

const acknowledgeAlert = (alertId: string) => {
  const alertIndex = realTimeAlerts.value.findIndex(a => a.id === alertId);
  if (alertIndex !== -1) {
    realTimeAlerts.value.splice(alertIndex, 1);
    ElMessage.success('警报已确认');
  }
};

const acknowledgeAllAlerts = () => {
  realTimeAlerts.value = [];
  ElMessage.success('所有警报已确认');
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 监控面板 */
.monitoring-panel {
  margin-bottom: var(--spacing-xxl);
}

.panel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 警报卡片 */
.alerts-card {
  background: var(--bg-white);
  border-left: 4px solid var(--warning-color);
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alerts-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.alert-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xs);
}

.alert-source {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.alert-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input,
.filter-item .el-date-picker {
  width: 100%;
}

/* 产品信息样式 */
.product-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.product-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.batch-number {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 进度信息样式 */
.progress-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 实时数据样式 */
.realtime-data {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.data-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 实时仪表盘样式 */
.realtime-dashboard {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.dashboard-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.dashboard-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-light);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-color-light);
}

.stat-value {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 工作站网格样式 */
.workstations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.station-card {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-base);
  border: 2px solid var(--border-color-light);
  transition: var(--transition-base);
}

.station-card:hover {
  box-shadow: var(--shadow-base);
}

.station-card.station-running {
  border-color: var(--success-color);
  background: var(--success-color-light);
}

.station-card.station-completed {
  border-color: var(--primary-color);
  background: var(--primary-color-light);
}

.station-card.station-pending {
  border-color: var(--info-color);
  background: var(--info-color-light);
}

.station-card.station-fault {
  border-color: var(--danger-color);
  background: var(--danger-color-light);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.station-header h4 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.station-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.station-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.info-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.station-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.station-progress .el-progress {
  flex: 1;
}

.station-progress .progress-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  min-width: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .workstations-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .panel-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.detail-card,
.station-card,
.stat-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
