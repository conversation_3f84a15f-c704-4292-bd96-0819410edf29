<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <div class="header-left">
        <!-- 侧边栏切换按钮 -->
        <el-button
          text
          @click="toggleSidebar"
          :icon="sidebarCollapsed ? Expand : Fold"
          class="sidebar-toggle-btn"
        />

        <div class="logo-section">
          <img src="/logo.png" alt="Logo" class="logo" />
          <span class="system-name">药食同源管理系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 用户信息 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :size="32" :src="authStore.userAvatar" />
            <span class="user-name">{{ authStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside 
        class="main-aside" 
        :class="{ 'mobile-show': mobileMenuVisible }"
        :width="sidebarCollapsed ? '64px' : '240px'"
      >

        
        <el-menu
          :default-active="currentRoute"
          :collapse="sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="item in menuItems" :key="item.path">
            <el-sub-menu 
              v-if="item.children && item.children.length > 0"
              :index="item.path"
            >
              <template #title>
                <el-icon><component :is="item.icon" /></el-icon>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.path"
                :index="child.path"
              >
                {{ child.title }}
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="item.path">
              <el-icon><component :is="item.icon" /></el-icon>
              <template #title>{{ item.title }}</template>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 主要内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
    
    <!-- 移动端遮罩层 -->
    <div 
      v-if="mobileMenuVisible" 
      class="mobile-overlay"
      @click="mobileMenuVisible = false"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ArrowDown,
  User,
  Setting,
  SwitchButton,
  Expand,
  Fold,
  Dashboard,
  Collection,
  Link,
  Management,
  DataAnalysis,
  Lock,
  Document,
  Monitor,
  CircleCheck,
  TrendCharts
} from '@element-plus/icons-vue';
import { useAuthStore } from '../stores/auth';

// 路由和状态管理
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// 侧边栏状态
const sidebarCollapsed = ref(false);
const mobileMenuVisible = ref(false);

// 当前路由
const currentRoute = computed(() => route.path);

// 菜单项配置
const menuItems = computed(() => {
  const items = [
    {
      path: '/dashboard/home',
      title: '仪表盘',
      icon: 'Dashboard',
      permissions: []
    },
    {
      path: '/knowledge',
      title: '数据标准与知识库',
      icon: 'Collection',
      permissions: [],
      children: [
        {
          path: '/knowledge/materials',
          title: '原料数据库',
          permissions: []
        },
        {
          path: '/knowledge/formulas',
          title: '配方数据库',
          permissions: []
        },
        {
          path: '/knowledge/efficacy',
          title: '功效物质验证',
          permissions: []
        }
      ]
    },
    {
      path: '/traceability',
      title: '供应链追溯与品控',
      icon: 'Link',
      permissions: [],
      children: [
        {
          path: '/traceability/materials',
          title: '原料溯源',
          permissions: []
        },
        {
          path: '/traceability/production',
          title: '生产追溯',
          permissions: []
        },
        {
          path: '/traceability/iot',
          title: 'IoT数据',
          permissions: []
        }
      ]
    },
    {
      path: '/production',
      title: '生产与运营管理',
      icon: 'Management',
      permissions: [],
      children: [
        {
          path: '/production/planning',
          title: '生产计划',
          permissions: []
        },
        {
          path: '/production/mes',
          title: 'MES系统',
          permissions: []
        },
        {
          path: '/production/bom',
          title: '工艺BOM',
          permissions: []
        },
        {
          path: '/production/alerts',
          title: '生产预警',
          permissions: []
        }
      ]
    },
    {
      path: '/customer',
      title: '智能客户服务',
      icon: 'User',
      permissions: [],
      children: [
        {
          path: '/customer/ai-advisor',
          title: 'AI养生顾问',
          permissions: []
        },
        {
          path: '/customer/health-profile',
          title: '健康档案管理',
          permissions: []
        }
      ]
    },
    {
      path: '/nutrition',
      title: '精准营养管理',
      icon: 'DataAnalysis',
      permissions: [],
      children: [
        {
          path: '/nutrition/health-data',
          title: '健康数据采集',
          permissions: []
        }
      ]
    },
    {
      path: '/quality',
      title: '质量管理',
      icon: 'CircleCheck',
      permissions: [],
      children: [
        {
          path: '/quality/batch-testing',
          title: '批次检测',
          permissions: []
        },
        {
          path: '/quality/certificates',
          title: '证书管理',
          permissions: []
        },
        {
          path: '/quality/protocols',
          title: '检测协议',
          permissions: []
        },
        {
          path: '/quality/standards',
          title: '质量标准',
          permissions: []
        }
      ]
    },
    {
      path: '/security',
      title: '数据安全与合规',
      icon: 'Lock',
      permissions: [],
      children: [
        {
          path: '/security/data-security',
          title: '数据安全管理',
          permissions: []
        }
      ]
    },
    {
      path: '/training',
      title: '培训与知识管理',
      icon: 'Document',
      permissions: [],
      children: [
        {
          path: '/training/online-training',
          title: '在线培训系统',
          permissions: []
        }
      ]
    },
    {
      path: '/analytics',
      title: '数据分析与决策',
      icon: 'TrendCharts',
      permissions: [],
      children: [
        {
          path: '/analytics/production',
          title: '生产数据分析',
          permissions: []
        }
      ]
    },
    {
      path: '/demo',
      title: '演示管理',
      icon: 'Monitor',
      permissions: [],
      children: [
        {
          path: '/demo/control',
          title: '演示控制面板',
          permissions: []
        }
      ]
    },
    {
      path: '/system',
      title: '系统管理',
      icon: 'Setting',
      permissions: [],
      children: [
        {
          path: '/system/users',
          title: '用户管理',
          permissions: []
        },
        {
          path: '/system/settings',
          title: '系统设置',
          permissions: []
        },
        {
          path: '/system/monitor',
          title: '系统监控',
          permissions: []
        },
        {
          path: '/system/api-docs',
          title: 'API文档',
          permissions: []
        },
        {
          path: '/system/data',
          title: '数据管理',
          permissions: []
        },
        {
          path: '/system/logs',
          title: '日志管理',
          permissions: []
        },
        {
          path: '/system/notifications',
          title: '通知管理',
          permissions: []
        }
      ]
    }
  ];

  // 根据用户权限过滤菜单项
  return items.filter(item => {
    if (item.permissions.length === 0) return true;
    // 如果是系统管理员，显示所有菜单
    if (authStore.isAdmin) return true;
    return authStore.hasAnyPermission(item.permissions);
  }).map(item => {
    if (item.children) {
      item.children = item.children.filter(child => {
        if (!child.permissions || child.permissions.length === 0) return true;
        // 如果是系统管理员，显示所有子菜单
        if (authStore.isAdmin) return true;
        return authStore.hasAnyPermission(child.permissions);
      });
    }
    return item;
  });
});

// 切换侧边栏
const toggleSidebar = () => {
  if (window.innerWidth <= 768) {
    mobileMenuVisible.value = !mobileMenuVisible.value;
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value;
  }
};

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.success('个人资料页面已打开');
      // 这里可以添加跳转到个人资料页面的逻辑
      break;
    case 'settings':
      router.push('/system/settings');
      break;
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '退出确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        await authStore.logout();
        router.push('/login');
      } catch (error) {
        // 用户取消退出
      }
      break;
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里添加布局初始化逻辑
});
</script>

<style scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle-btn {
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
  background-color: #f5f7fa;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.system-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
  display: flex;
}

.main-aside {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
  height: 100%;
}



.sidebar-menu {
  border: none;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-menu .el-menu-item,
.sidebar-menu .el-sub-menu__title {
  height: 48px;
  line-height: 48px;
}

.main-content {
  background: #f5f7fa;
  padding: 24px;
  overflow-y: auto;
  flex: 1;
  height: 100%;
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-overlay {
    display: block;
  }
  
  .main-aside {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .main-aside.mobile-show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    width: 100%;
  }
  
  .system-name {
    display: none;
  }
  
  .main-header {
    padding: 0 16px;
  }
}
</style>
