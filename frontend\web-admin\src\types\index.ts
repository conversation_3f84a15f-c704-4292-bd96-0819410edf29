/**
 * 类型定义统一导出 (Unified Type Exports)
 * 解决模块导入问题
 */

// 重新导出用户相关类型
export type { UserInfo } from './user';
export { UserRole, Permission, ROLE_PERMISSIONS, ROLE_LABELS } from './user';

// 重新导出认证相关类型
export type {
  LoginForm,
  LoginRequest,
  LoginResponse,
  AuthState,
  CaptchaResponse,
  PasswordResetRequest,
  PasswordChangeRequest,
  RegisterRequest,
  NotificationSettings,
  UserPreferences,
  SessionInfo,
  LoginHistory
} from './auth';
