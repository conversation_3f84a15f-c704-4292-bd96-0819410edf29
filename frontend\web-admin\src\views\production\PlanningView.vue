<template>
  <PageContainer
    title="生产计划"
    description="管理和监控生产计划执行情况"
    :breadcrumb="breadcrumb"
    icon="Setting"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建计划
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出计划
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="计划总数"
          :value="stats.total"
          icon="Calendar"
          theme="primary"
          description="系统中的生产计划总数"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="执行中"
          :value="stats.inProgress"
          icon="PlayCircle"
          theme="success"
          description="正在执行的生产计划"
          :clickable="true"
          @click="filterByStatus('in_progress')"
        />
        <StatCard
          title="待审批"
          :value="stats.pending"
          icon="Clock"
          theme="warning"
          description="等待审批的生产计划"
          :clickable="true"
          @click="filterByStatus('pending_review')"
        />
        <StatCard
          title="延期计划"
          :value="stats.delayed"
          icon="Warning"
          theme="danger"
          description="进度延期的生产计划"
          :clickable="true"
          @click="filterByStatus('delayed')"
        />
      </div>
    </div>

    <!-- 计划类型导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>计划类型</h3>
        <div class="category-tabs">
          <el-button
            v-for="type in planTypes"
            :key="type.value"
            :type="selectedType === type.value ? 'primary' : 'default'"
            @click="selectType(type.value)"
          >
            {{ type.label }}
          </el-button>
          <el-button
            :type="selectedType === '' ? 'primary' : 'default'"
            @click="selectType('')"
          >
            全部类型
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in planStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.priority"
            placeholder="选择优先级"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="priority in priorities"
              :key="priority.value"
              :label="priority.label"
              :value="priority.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadData"
          />
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索计划号、产品名称"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="生产计划列表"
      description="查看和管理所有生产计划"
      :show-selection="true"
      :show-index="true"
      :search-fields="['planNumber', 'planName', 'productInfo.productName']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 计划类型列插槽 -->
      <template #planType="{ row }">
        <el-tag type="info">
          {{ getPlanTypeLabel(row.planType) }}
        </el-tag>
      </template>

      <!-- 产品信息列插槽 -->
      <template #productInfo="{ row }">
        <div class="product-info">
          <div class="product-name">{{ row.productInfo.productName }}</div>
          <div class="product-spec">{{ row.productInfo.specification }}</div>
        </div>
      </template>

      <!-- 优先级列插槽 -->
      <template #priority="{ row }">
        <el-tag :type="getPriorityType(row.priority)">
          {{ getPriorityLabel(row.priority) }}
        </el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 进度列插槽 -->
      <template #progress="{ row }">
        <div class="progress-info">
          <el-progress
            :percentage="row.progress.overallProgress"
            :status="getProgressStatus(row.progress.overallProgress)"
            :stroke-width="8"
          />
          <div class="progress-text">
            {{ row.progress.completedMilestones }}/{{ row.progress.totalMilestones }} 里程碑
          </div>
        </div>
      </template>

      <!-- 时间线列插槽 -->
      <template #timeline="{ row }">
        <div class="timeline-info">
          <div class="timeline-date">{{ row.timeline.plannedStartDate }} ~ {{ row.timeline.plannedEndDate }}</div>
          <div v-if="row.progress.delayDays > 0" class="delay-warning">
            <el-tag type="warning" size="small">延期{{ row.progress.delayDays }}天</el-tag>
          </div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          v-if="row.status !== 'completed' && row.status !== 'cancelled'"
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          v-if="row.status === 'draft'"
          type="danger"
          size="small"
          :icon="Delete"
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="生产计划详情"
      width="1200px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="计划编号">
              {{ currentRow.planNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="计划名称">
              {{ currentRow.planName }}
            </el-descriptions-item>
            <el-descriptions-item label="计划类型">
              {{ getPlanTypeLabel(currentRow.planType) }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(currentRow.priority)">
                {{ getPriorityLabel(currentRow.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentRow.status)">
                {{ getStatusLabel(currentRow.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ currentRow.createdBy }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 产品信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>产品信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="产品名称">
              {{ currentRow.productInfo.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="产品编码">
              {{ currentRow.productInfo.productCode }}
            </el-descriptions-item>
            <el-descriptions-item label="规格">
              {{ currentRow.productInfo.specification }}
            </el-descriptions-item>
            <el-descriptions-item label="批次大小">
              {{ currentRow.productInfo.batchSize }} {{ currentRow.productInfo.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="配方版本">
              {{ currentRow.productInfo.formulaVersion }}
            </el-descriptions-item>
            <el-descriptions-item label="保质期">
              {{ currentRow.productInfo.shelfLife }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 计划详情 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>计划详情</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="计划数量">
              {{ currentRow.planDetails.plannedQuantity }} {{ currentRow.productInfo.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="计划批次">
              {{ currentRow.planDetails.plannedBatches }} 批次
            </el-descriptions-item>
            <el-descriptions-item label="预计工期">
              {{ currentRow.planDetails.estimatedDuration }} 天
            </el-descriptions-item>
            <el-descriptions-item label="生产线">
              {{ currentRow.planDetails.productionLine }}
            </el-descriptions-item>
            <el-descriptions-item label="车间区域">
              {{ currentRow.planDetails.workshopArea }}
            </el-descriptions-item>
            <el-descriptions-item label="班次安排">
              {{ currentRow.planDetails.shiftArrangement }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 进度信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>进度信息</h3>
          </template>
          <div class="progress-section">
            <div class="progress-overview">
              <div class="progress-item">
                <span class="progress-label">总体进度</span>
                <el-progress
                  :percentage="currentRow.progress.overallProgress"
                  :status="getProgressStatus(currentRow.progress.overallProgress)"
                  :stroke-width="12"
                />
              </div>
              <div class="progress-stats">
                <div class="stat-item">
                  <span class="stat-label">当前阶段</span>
                  <span class="stat-value">{{ currentRow.progress.currentPhase }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">里程碑进度</span>
                  <span class="stat-value">{{ currentRow.progress.completedMilestones }}/{{ currentRow.progress.totalMilestones }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">延期天数</span>
                  <span class="stat-value" :class="{ 'delay-warning': currentRow.progress.delayDays > 0 }">
                    {{ currentRow.progress.delayDays }} 天
                  </span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">执行效率</span>
                  <span class="stat-value">{{ currentRow.progress.efficiency }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Calendar,
  Plus,
  Download,
  Search,
  PlayCircle,
  Clock,
  Warning,
  View,
  Edit,
  Delete
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getProductionPlans,
  planTypes,
  planStatuses,
  priorities,
  type ProductionPlan
} from '../../mock/production-plans';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '生产与运营管理', path: '/production' },
  { title: '生产计划', path: '/production/planning' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<ProductionPlan | null>(null);
const tableRef = ref();
const selectedType = ref('');

// 表格数据
const tableData = ref<ProductionPlan[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  planType: '',
  status: '',
  priority: '',
  dateRange: null as string[] | null,
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const plans = tableData.value;
  return {
    total: plans.length,
    inProgress: plans.filter(p => p.status === 'in_progress').length,
    pending: plans.filter(p => p.status === 'pending_review').length,
    delayed: plans.filter(p => p.status === 'delayed').length
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'planNumber',
    label: '计划编号',
    width: 140,
    fixed: 'left'
  },
  {
    prop: 'planName',
    label: '计划名称',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'planType',
    label: '计划类型',
    width: 100,
    slot: 'planType'
  },
  {
    prop: 'productInfo',
    label: '产品信息',
    width: 180,
    slot: 'productInfo'
  },
  {
    prop: 'priority',
    label: '优先级',
    width: 80,
    slot: 'priority'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'progress',
    label: '进度',
    width: 150,
    slot: 'progress'
  },
  {
    prop: 'timeline',
    label: '计划时间',
    width: 200,
    slot: 'timeline'
  },
  {
    prop: 'createdBy',
    label: '创建人',
    width: 100
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getProductionPlans({
      planType: filters.planType || selectedType.value,
      status: filters.status,
      priority: filters.priority,
      dateRange: filters.dateRange,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.planType = '';
  filters.status = '';
  filters.priority = '';
  filters.dateRange = null;
  filters.keyword = '';
  selectedType.value = '';
  loadData();
};

const selectType = (type: string) => {
  selectedType.value = type;
  filters.planType = type;
  loadData();
};

const getPlanTypeLabel = (type: string) => {
  const item = planTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getPriorityLabel = (priority: string) => {
  const item = priorities.find(p => p.value === priority);
  return item ? item.label : priority;
};

const getPriorityType = (priority: string) => {
  const item = priorities.find(p => p.value === priority);
  return item ? item.type : 'info';
};

const getStatusLabel = (status: string) => {
  const item = planStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = planStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getProgressStatus = (percentage: number) => {
  if (percentage === 100) return 'success';
  if (percentage >= 80) return 'warning';
  return '';
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const handleAdd = () => {
  ElMessage.success('新建生产计划对话框已打开');
  // 这里可以添加打开新建对话框的逻辑
};

const handleExport = () => {
  ElMessage.success('生产计划数据导出已开始');
  // 这里可以添加实际的导出逻辑
};

const handleSelectionChange = (selection: ProductionPlan[]) => {
  console.log('选中的计划:', selection);
};

const handleRowClick = (row: ProductionPlan) => {
  console.log('点击的计划:', row);
};

const handleView = (row: ProductionPlan) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleEdit = (row: ProductionPlan) => {
  ElMessage.info(`编辑计划: ${row.planName}`);
};

const handleDelete = async (row: ProductionPlan) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除计划 "${row.planName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    // 用户取消删除
  }
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input,
.filter-item .el-date-picker {
  width: 100%;
}

/* 产品信息样式 */
.product-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.product-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.product-spec {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 进度信息样式 */
.progress-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 时间线信息样式 */
.timeline-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.timeline-date {
  font-size: var(--font-size-sm);
  color: var(--text-regular);
}

.delay-warning {
  display: flex;
  justify-content: flex-start;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 进度详情样式 */
.progress-section {
  padding: var(--spacing-lg);
}

.progress-overview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.progress-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: var(--bg-light);
  border-radius: var(--border-radius-base);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.stat-value.delay-warning {
  color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }

  .progress-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .progress-stats {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.detail-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
