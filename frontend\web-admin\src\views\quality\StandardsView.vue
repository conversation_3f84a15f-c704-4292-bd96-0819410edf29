<template>
  <PageContainer
    title="质量标准管理"
    description="制定、维护和管理产品质量标准"
    :breadcrumb="breadcrumb"
    icon="Document"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建标准
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出标准
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="标准总数"
          :value="stats.total"
          icon="Document"
          theme="primary"
          description="系统中的质量标准总数"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="生效中"
          :value="stats.effective"
          icon="CircleCheck"
          theme="success"
          description="当前生效的标准数量"
          :clickable="true"
          @click="filterByStatus('effective')"
        />
        <StatCard
          title="审核中"
          :value="stats.review"
          icon="Clock"
          theme="warning"
          description="正在审核的标准数量"
          :clickable="true"
          @click="filterByStatus('review')"
        />
        <StatCard
          title="即将过期"
          :value="stats.expiring"
          icon="Warning"
          theme="danger"
          description="30天内即将过期的标准"
          :clickable="true"
          @click="filterExpiring"
        />
      </div>
    </div>

    <!-- 分类导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>标准分类</h3>
        <div class="category-tabs">
          <el-button
            v-for="category in qualityStandardCategories"
            :key="category.value"
            :type="selectedCategory === category.value ? 'primary' : 'default'"
            @click="selectCategory(category.value)"
          >
            {{ category.label }}
          </el-button>
          <el-button
            :type="selectedCategory === '' ? 'primary' : 'default'"
            @click="selectCategory('')"
          >
            全部
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.type"
            placeholder="选择标准类型"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="type in standardTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.status"
            placeholder="选择状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in standardStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索标准名称、编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="质量标准列表"
      description="查看和管理所有质量标准"
      :show-selection="true"
      :show-index="true"
      :search-fields="['name', 'code', 'description']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 分类列插槽 -->
      <template #category="{ row }">
        <el-tag type="info">
          {{ getCategoryLabel(row.category) }}
        </el-tag>
      </template>

      <!-- 类型列插槽 -->
      <template #type="{ row }">
        <el-tag>
          {{ getTypeLabel(row.type) }}
        </el-tag>
      </template>

      <!-- 版本列插槽 -->
      <template #version="{ row }">
        <el-tag type="success">{{ row.version }}</el-tag>
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusLabel(row.status) }}
        </el-tag>
      </template>

      <!-- 有效期列插槽 -->
      <template #validity="{ row }">
        <div class="validity-info">
          <div class="validity-date">{{ row.effectiveDate }} ~ {{ row.expiryDate || '长期' }}</div>
          <div v-if="isExpiringSoon(row)" class="validity-warning">
            <el-tag type="warning" size="small">即将过期</el-tag>
          </div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="success"
          size="small"
          :icon="CopyDocument"
          @click="handleCopy(row)"
        >
          复制
        </el-button>
      </template>
    </DataTable>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Document,
  Plus,
  Download,
  Search,
  View,
  Edit,
  CopyDocument,
  CircleCheck,
  Clock,
  Warning
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getQualityStandards,
  qualityStandardCategories,
  standardTypes,
  standardStatuses,
  type QualityStandard
} from '../../mock/quality-standards';

// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '质量控制与检测', path: '/quality' },
  { title: '质量标准', path: '/quality/standards' }
]);

// 响应式数据
const loading = ref(false);
const tableRef = ref();
const selectedCategory = ref('');

// 表格数据
const tableData = ref<QualityStandard[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  category: '',
  type: '',
  status: '',
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const standards = tableData.value;
  return {
    total: standards.length,
    effective: standards.filter(s => s.status === 'effective').length,
    review: standards.filter(s => s.status === 'review').length,
    expiring: standards.filter(s => isExpiringSoon(s)).length
  };
});

// 表格列配置
const tableColumns = ref([
  {
    prop: 'code',
    label: '标准编码',
    width: 140,
    fixed: 'left'
  },
  {
    prop: 'name',
    label: '标准名称',
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    prop: 'category',
    label: '分类',
    width: 100,
    slot: 'category'
  },
  {
    prop: 'type',
    label: '类型',
    width: 100,
    slot: 'type'
  },
  {
    prop: 'version',
    label: '版本',
    width: 80,
    slot: 'version'
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'validity',
    label: '有效期',
    width: 200,
    slot: 'validity'
  },
  {
    prop: 'approver',
    label: '批准人',
    width: 100
  }
]);

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getQualityStandards({
      category: filters.category || selectedCategory.value,
      type: filters.type,
      status: filters.status,
      keyword: filters.keyword
    });
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.category = '';
  filters.type = '';
  filters.status = '';
  filters.keyword = '';
  selectedCategory.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.status = status;
  loadData();
};

const filterExpiring = () => {
  ElMessage.info('筛选即将过期的标准');
};

const selectCategory = (category: string) => {
  selectedCategory.value = category;
  filters.category = category;
  loadData();
};

const getCategoryLabel = (category: string) => {
  const item = qualityStandardCategories.find(c => c.value === category);
  return item ? item.label : category;
};

const getTypeLabel = (type: string) => {
  const item = standardTypes.find(t => t.value === type);
  return item ? item.label : type;
};

const getStatusLabel = (status: string) => {
  const item = standardStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = standardStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const isExpiringSoon = (standard: QualityStandard) => {
  if (!standard.expiryDate) return false;
  const expiryDate = new Date(standard.expiryDate);
  const now = new Date();
  const diffTime = expiryDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 30 && diffDays > 0;
};

const handleAdd = () => {
  ElMessageBox.prompt('请输入标准名称', '新建质量标准', {
    confirmButtonText: '创建',
    cancelButtonText: '取消',
    inputPattern: /\S+/,
    inputErrorMessage: '标准名称不能为空'
  }).then(({ value }) => {
    const newStandard: QualityStandard = {
      id: `std_${Date.now()}`,
      code: `STD${Date.now().toString().slice(-6)}`,
      name: value,
      category: 'custom',
      type: 'enterprise',
      version: '1.0',
      status: 'draft',
      description: '新建的质量标准',
      content: '标准内容待完善',
      effectiveDate: new Date().toISOString().split('T')[0],
      expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      createdBy: '当前用户',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    tableData.value.unshift(newStandard);
    ElMessage.success(`质量标准 "${value}" 创建成功`);
  }).catch(() => {
    // 用户取消
  });
};

const handleEdit = (row: QualityStandard) => {
  ElMessage.info(`编辑标准: ${row.name}`);
};

const handleView = (row: QualityStandard) => {
  ElMessage.info(`查看标准: ${row.name}`);
};

const handleCopy = (row: QualityStandard) => {
  ElMessage.info(`复制标准: ${row.name}`);
};

const handleExport = () => {
  ElNotification({
    title: '导出成功',
    message: '质量标准数据已导出到 quality_standards.xlsx',
    type: 'success',
    duration: 3000
  });
};

const handleSelectionChange = (selection: QualityStandard[]) => {
  console.log('选中的标准:', selection);
};

const handleRowClick = (row: QualityStandard) => {
  console.log('点击的标准:', row);
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-tabs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input {
  width: 100%;
}

/* 有效期信息样式 */
.validity-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.validity-date {
  font-size: var(--font-size-sm);
  color: var(--text-regular);
}

.validity-warning {
  display: flex;
  justify-content: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.category-tabs .el-button {
  transition: var(--transition-base);
}

.validity-info {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
