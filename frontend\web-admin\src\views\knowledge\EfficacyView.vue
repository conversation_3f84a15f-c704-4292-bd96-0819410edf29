<template>
  <PageContainer
    title="功效物质验证平台"
    description="中药功效物质科学验证与评价平台"
    :breadcrumb="breadcrumb"
    icon="Experiment"
  >
    <template #actions>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        新建验证项目
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        导出数据
      </el-button>
      <el-button :icon="Upload" @click="handleImport">
        导入数据
      </el-button>
    </template>

    <!-- 统计卡片 -->
    <div class="stats-section mb-xxl">
      <div class="stats-grid">
        <StatCard
          title="验证项目总数"
          :value="stats.total"
          icon="Experiment"
          theme="primary"
          description="平台中的验证项目总数"
          :clickable="true"
          @click="resetFilters"
        />
        <StatCard
          title="已完成验证"
          :value="stats.completed"
          icon="CircleCheck"
          theme="success"
          description="已完成验证的项目数量"
          :clickable="true"
          @click="filterByStatus('completed')"
        />
        <StatCard
          title="进行中"
          :value="stats.ongoing"
          icon="Loading"
          theme="warning"
          description="正在进行验证的项目"
          :clickable="true"
          @click="filterByStatus('ongoing')"
        />
        <StatCard
          title="A级证据"
          :value="stats.gradeA"
          icon="Medal"
          theme="info"
          description="获得A级证据的项目"
          :clickable="true"
          @click="filterByGrade('A')"
        />
      </div>
    </div>

    <!-- 验证级别导航 -->
    <el-card class="category-card mb-lg">
      <div class="category-nav">
        <h3>验证级别</h3>
        <div class="category-tabs">
          <el-button
            v-for="level in validationLevels"
            :key="level.value"
            :type="selectedLevel === level.value ? 'primary' : 'default'"
            @click="selectLevel(level.value)"
          >
            {{ level.label }}
          </el-button>
          <el-button
            :type="selectedLevel === '' ? 'primary' : 'default'"
            @click="selectLevel('')"
          >
            全部级别
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 筛选器 -->
    <el-card class="filter-card mb-lg">
      <div class="filter-row">
        <div class="filter-item">
          <el-select
            v-model="filters.validationStatus"
            placeholder="选择验证状态"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="status in validationStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select
            v-model="filters.evidenceGrade"
            placeholder="选择证据等级"
            clearable
            @change="loadData"
          >
            <el-option
              v-for="grade in evidenceGrades"
              :key="grade.value"
              :label="grade.label"
              :value="grade.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索化合物名称、编码"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="filter-item">
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <DataTable
      ref="tableRef"
      :data="tableData"
      :columns="tableColumns"
      :loading="loading"
      title="功效验证列表"
      description="查看和管理功效物质验证数据"
      :show-selection="true"
      :show-index="true"
      :search-fields="['componentName', 'efficacy', 'testMethod']"
      @refresh="loadData"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <!-- 验证级别列插槽 -->
      <template #validationLevel="{ row }">
        <el-tag :type="getValidationLevelType(row.validationLevel)">
          {{ getValidationLevelLabel(row.validationLevel) }}
        </el-tag>
      </template>

      <!-- 证据等级列插槽 -->
      <template #evidenceGrade="{ row }">
        <el-tag :type="getEvidenceGradeType(row.evidenceGrade)">
          {{ getEvidenceGradeLabel(row.evidenceGrade) }}
        </el-tag>
      </template>

      <!-- 验证状态列插槽 -->
      <template #validationStatus="{ row }">
        <el-tag :type="getStatusType(row.validationStatus)">
          {{ getStatusLabel(row.validationStatus) }}
        </el-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          :icon="View"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="warning"
          size="small"
          :icon="Edit"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <el-button
          type="success"
          size="small"
          :icon="Document"
          @click="handleReport(row)"
        >
          报告
        </el-button>
      </template>
    </DataTable>

    <!-- 验证详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="功效验证详情"
      width="1000px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentRow" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="化合物编码">
              {{ currentRow.compoundCode }}
            </el-descriptions-item>
            <el-descriptions-item label="化合物名称">
              {{ currentRow.compoundName }}
            </el-descriptions-item>
            <el-descriptions-item label="化学名称">
              {{ currentRow.chemicalName }}
            </el-descriptions-item>
            <el-descriptions-item label="CAS号">
              {{ currentRow.casNumber || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="分子式">
              {{ currentRow.molecularFormula }}
            </el-descriptions-item>
            <el-descriptions-item label="分子量">
              {{ currentRow.molecularWeight }}
            </el-descriptions-item>
            <el-descriptions-item label="验证级别">
              <el-tag :type="getValidationLevelType(currentRow.validationLevel)">
                {{ getValidationLevelLabel(currentRow.validationLevel) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="证据等级">
              <el-tag :type="getEvidenceGradeType(currentRow.evidenceGrade)">
                {{ getEvidenceGradeLabel(currentRow.evidenceGrade) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="验证状态">
              <el-tag :type="getStatusType(currentRow.validationStatus)">
                {{ getStatusLabel(currentRow.validationStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="当前阶段">
              {{ currentRow.researchProgress.currentPhase }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ new Date(currentRow.createdAt).toLocaleDateString() }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ new Date(currentRow.updatedAt).toLocaleDateString() }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 验证结果 -->
        <el-card class="detail-card mb-lg">
          <template #header>
            <h3>验证结果</h3>
          </template>
          <div class="result-content">
            <div class="result-summary">
              <h4>药理活性</h4>
              <p><strong>主要活性：</strong>{{ currentRow.pharmacologyData.primaryActivity.join(', ') }}</p>
              <p><strong>次要活性：</strong>{{ currentRow.pharmacologyData.secondaryActivity.join(', ') }}</p>
            </div>
            <div class="result-data" v-if="currentRow.mechanismStudies.length > 0">
              <h4>机制研究</h4>
              <el-table :data="currentRow.mechanismStudies" border>
                <el-table-column prop="studyType" label="研究类型" />
                <el-table-column prop="objective" label="研究目标" />
                <el-table-column prop="methodology" label="研究方法" />
                <el-table-column prop="conclusion" label="结论" />
              </el-table>
            </div>
          </div>
        </el-card>

        <!-- 文献支持 -->
        <el-card class="detail-card" v-if="currentRow.literatureData.length > 0">
          <template #header>
            <h3>文献支持</h3>
          </template>
          <el-table :data="currentRow.literatureData" border>
            <el-table-column prop="title" label="文献标题" />
            <el-table-column prop="authors" label="作者" :formatter="(row) => row.authors.join(', ')" />
            <el-table-column prop="journal" label="期刊" />
            <el-table-column prop="year" label="年份" />
            <el-table-column prop="evidenceLevel" label="证据等级" />
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit(currentRow)">编辑</el-button>
        <el-button type="success" @click="handleReport(currentRow)">生成报告</el-button>
      </template>
    </el-dialog>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Experiment,
  Plus,
  Download,
  Upload,
  Search,
  View,
  Edit,
  Document,
  CircleCheck,
  Loading,
  Medal
} from '@element-plus/icons-vue';
import PageContainer from '../../components/common/PageContainer.vue';
import DataTable from '../../components/common/DataTable.vue';
import StatCard from '../../components/common/StatCard.vue';
import {
  getEfficacyValidations,
  validationStatuses,
  validationLevels,
  evidenceGrades,
  mockEfficacyValidations,
  type EfficacyValidation
} from '../../mock/efficacy-validation';



// 面包屑导航
const breadcrumb = ref([
  { title: '首页', path: '/dashboard/home' },
  { title: '数据标准与知识库', path: '/knowledge' },
  { title: '功效物质验证', path: '/knowledge/efficacy' }
]);

// 响应式数据
const loading = ref(false);
const detailVisible = ref(false);
const currentRow = ref<EfficacyValidation | null>(null);
const tableRef = ref();
const selectedLevel = ref('');

// 表格数据
const tableData = ref<EfficacyValidation[]>([]);
const total = ref(0);

// 筛选器
const filters = reactive({
  validationStatus: '',
  validationLevel: '',
  evidenceGrade: '',
  keyword: ''
});

// 统计数据
const stats = computed(() => {
  const validations = tableData.value;
  return {
    total: validations.length,
    completed: validations.filter(v => v.validationStatus === 'completed').length,
    ongoing: validations.filter(v => v.validationStatus === 'ongoing').length,
    gradeA: validations.filter(v => v.evidenceGrade === 'A').length
  };
});





// 表格列配置
const tableColumns = ref([
  {
    prop: 'compoundCode',
    label: '化合物编码',
    width: 120,
    fixed: 'left'
  },
  {
    prop: 'compoundName',
    label: '化合物名称',
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'chemicalName',
    label: '化学名称',
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    prop: 'validationLevel',
    label: '验证级别',
    width: 100,
    slot: 'validationLevel'
  },
  {
    prop: 'evidenceGrade',
    label: '证据等级',
    width: 100,
    slot: 'evidenceGrade'
  },
  {
    prop: 'validationStatus',
    label: '验证状态',
    width: 100,
    slot: 'validationStatus'
  },
  {
    prop: 'researchProgress.currentPhase',
    label: '当前阶段',
    width: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'updatedAt',
    label: '更新时间',
    width: 120,
    formatter: (row: EfficacyValidation) => {
      return new Date(row.updatedAt).toLocaleDateString();
    }
  },
  {
    prop: 'actions',
    label: '操作',
    width: 200,
    slot: 'actions',
    fixed: 'right'
  }
]);

// 使用模拟数据
const mockData = mockEfficacyValidations;

// 方法
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));

    let filteredData = [...mockData];

    // 筛选逻辑
    if (filters.validationStatus) {
      filteredData = filteredData.filter(item => item.validationStatus === filters.validationStatus);
    }

    if (filters.validationLevel) {
      filteredData = filteredData.filter(item => item.validationLevel === filters.validationLevel);
    }

    if (filters.evidenceGrade) {
      filteredData = filteredData.filter(item => item.evidenceGrade === filters.evidenceGrade);
    }

    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase();
      filteredData = filteredData.filter(item =>
        item.compoundName.toLowerCase().includes(keyword) ||
        item.chemicalName.toLowerCase().includes(keyword) ||
        item.compoundCode.toLowerCase().includes(keyword)
      );
    }

    tableData.value = filteredData;
    total.value = filteredData.length;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  loadData();
};

const resetFilters = () => {
  filters.validationStatus = '';
  filters.validationLevel = '';
  filters.evidenceGrade = '';
  filters.keyword = '';
  selectedLevel.value = '';
  loadData();
};

const filterByStatus = (status: string) => {
  filters.validationStatus = status;
  loadData();
};

const filterByGrade = (grade: string) => {
  filters.evidenceGrade = grade;
  loadData();
};

const selectLevel = (level: string) => {
  selectedLevel.value = selectedLevel.value === level ? '' : level;
  filters.validationLevel = level;
  loadData();
};

const getTestTypeLabel = (type: string) => {
  // 简化的类型标签映射
  const typeMap: Record<string, string> = {
    'in_vitro': '体外实验',
    'in_vivo': '体内实验',
    'molecular': '分子实验',
    'clinical': '临床试验'
  };
  return typeMap[type] || type;
};

const getStatusLabel = (status: string) => {
  const item = validationStatuses.find(s => s.value === status);
  return item ? item.label : status;
};

const getStatusType = (status: string) => {
  const item = validationStatuses.find(s => s.value === status);
  return item ? item.type : 'info';
};

const getValidationLevelLabel = (level: string) => {
  const item = validationLevels.find(l => l.value === level);
  return item ? item.label : level;
};

const getValidationLevelType = (level: string) => {
  const item = validationLevels.find(l => l.value === level);
  return item ? item.type : 'info';
};

const getEvidenceGradeLabel = (grade: string) => {
  const item = evidenceGrades.find(g => g.value === grade);
  return item ? item.label : grade;
};

const getEvidenceGradeType = (grade: string) => {
  const item = evidenceGrades.find(g => g.value === grade);
  return item ? item.type : 'info';
};

const handleAdd = () => {
  ElMessage.success('新建验证项目对话框已打开');
  // 这里可以添加打开新建对话框的逻辑
};

const handleEdit = (row: EfficacyValidation) => {
  ElMessage.info(`编辑验证: ${row.compoundName}`);
  detailVisible.value = false;
};

const handleView = (row: EfficacyValidation) => {
  currentRow.value = row;
  detailVisible.value = true;
};

const handleReport = (row: EfficacyValidation) => {
  ElMessage.info(`生成${row.compoundName}验证报告`);
};

const handleExport = () => {
  ElMessage.success('验证数据导出已开始');
  // 这里可以添加实际的导出逻辑
};

const handleImport = () => {
  ElMessage.success('验证数据导入功能已启动');
  // 这里可以添加实际的导入逻辑
};

const handleSelectionChange = (selection: EfficacyValidation[]) => {
  console.log('选中的验证:', selection);
};

const handleRowClick = (row: EfficacyValidation) => {
  console.log('点击的验证:', row);
};

const handleCloseDetail = () => {
  detailVisible.value = false;
  currentRow.value = null;
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* 统计卡片区域 */
.stats-section {
  margin-bottom: var(--spacing-xxl);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

/* 功效分类导航 */
.category-card {
  background: var(--bg-white);
}

.category-nav h3 {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.category-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: var(--transition-base);
  background: var(--bg-white);
}

.category-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
}

.category-item.active {
  border-color: var(--primary-color);
  background: var(--primary-extra-light);
}

.category-icon {
  color: var(--primary-color);
}

.category-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.category-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 筛选器样式 */
.filter-card {
  background: var(--bg-white);
}

.filter-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item .el-select,
.filter-item .el-input {
  width: 100%;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: var(--spacing-lg);
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

/* 验证结果样式 */
.result-content {
  padding: var(--spacing-lg) 0;
}

.result-summary,
.result-data {
  margin-bottom: var(--spacing-lg);
}

.result-summary h4,
.result-data h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.result-summary p {
  margin: 0;
  line-height: var(--line-height-relaxed);
  color: var(--text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .category-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
}

/* 对话框样式覆盖 */
:deep(.el-dialog) {
  .el-dialog__body {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
}

/* 动画效果 */
.detail-card {
  animation: slideInUp 0.3s ease-out;
}

.category-item {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
