/**
 * 认证状态管理 (Authentication Store)
 */

import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import type {
  AuthState,
  LoginRequest,
  LoginResponse,
  PasswordChangeRequest
} from '../types/auth';
import type { UserInfo } from '../types/user';
import { Permission } from '../types/user';
import { 
  loginApi, 
  logoutApi, 
  getUserInfoApi, 
  refreshTokenApi,
  changePasswordApi,
  validateTokenApi
} from '../api/auth';

// Token存储键名 (Token Storage Keys)
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_INFO_KEY = 'user_info';

export const useAuthStore = defineStore('auth', () => {
  // 状态定义 (State Definition)
  const isAuthenticated = ref<boolean>(false);
  const user = ref<UserInfo | null>(null);
  const token = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const loading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // 计算属性 (Computed Properties)
  const permissions = computed<Permission[]>(() => {
    return user.value?.permissions || [];
  });

  const userRole = computed(() => {
    return user.value?.role;
  });

  const isAdmin = computed(() => {
    return user.value?.role === 'system_admin';
  });

  const userName = computed(() => {
    return user.value?.realName || user.value?.username || '';
  });

  const userAvatar = computed(() => {
    return user.value?.avatar || '/avatars/default.jpg';
  });

  // 权限检查方法 (Permission Check Methods)
  const hasPermission = (permission: Permission): boolean => {
    return permissions.value.includes(permission);
  };

  const hasAnyPermission = (permissionList: Permission[]): boolean => {
    return permissionList.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissionList: Permission[]): boolean => {
    return permissionList.every(permission => hasPermission(permission));
  };

  // 清除认证数据 (Clear Auth Data)
  const clearAuthData = (): void => {
    isAuthenticated.value = false;
    user.value = null;
    token.value = null;
    refreshToken.value = null;
    error.value = null;
    
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_INFO_KEY);
  };

  // 初始化认证状态 (Initialize Auth State)
  const initializeAuth = async (): Promise<void> => {
    try {
      const storedToken = localStorage.getItem(TOKEN_KEY);
      const storedRefreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      const storedUserInfo = localStorage.getItem(USER_INFO_KEY);

      if (storedToken && storedUserInfo) {
        // 验证Token有效性
        const isValid = await validateTokenApi(storedToken);
        
        if (isValid) {
          token.value = storedToken;
          refreshToken.value = storedRefreshToken;
          user.value = JSON.parse(storedUserInfo);
          isAuthenticated.value = true;
        } else {
          // Token无效，尝试刷新
          if (storedRefreshToken) {
            await refreshTokenAction(storedRefreshToken);
          } else {
            clearAuthData();
          }
        }
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error);
      clearAuthData();
    }
  };

  // 登录操作 (Login Action)
  const login = async (loginData: LoginRequest): Promise<void> => {
    try {
      loading.value = true;
      error.value = null;

      const response: LoginResponse = await loginApi(loginData);
      
      // 保存认证信息
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      user.value = response.user;
      isAuthenticated.value = true;

      // 持久化存储
      localStorage.setItem(TOKEN_KEY, response.token);
      localStorage.setItem(REFRESH_TOKEN_KEY, response.refreshToken);
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(response.user));

      ElMessage.success(`欢迎回来，${response.user.realName}！`);
    } catch (err: any) {
      error.value = err.message || '登录失败';
      ElMessage.error(error.value);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 登出操作 (Logout Action)
  const logout = async (): Promise<void> => {
    try {
      loading.value = true;
      
      // 调用登出API
      await logoutApi();
      
      // 清除本地数据
      clearAuthData();
      
      ElMessage.success('已安全退出');
    } catch (err: any) {
      console.error('登出失败:', err);
      // 即使API调用失败，也要清除本地数据
      clearAuthData();
    } finally {
      loading.value = false;
    }
  };

  // 刷新Token操作 (Refresh Token Action)
  const refreshTokenAction = async (currentRefreshToken: string): Promise<void> => {
    try {
      const response = await refreshTokenApi(currentRefreshToken);
      
      token.value = response.token;
      refreshToken.value = response.refreshToken;
      
      localStorage.setItem(TOKEN_KEY, response.token);
      localStorage.setItem(REFRESH_TOKEN_KEY, response.refreshToken);
    } catch (error) {
      console.error('刷新Token失败:', error);
      clearAuthData();
      throw error;
    }
  };

  // 获取用户信息 (Get User Info)
  const fetchUserInfo = async (): Promise<void> => {
    if (!token.value) {
      throw new Error('未找到认证Token');
    }

    try {
      loading.value = true;
      const userInfo = await getUserInfoApi(token.value);
      
      user.value = userInfo;
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
    } catch (err: any) {
      error.value = err.message || '获取用户信息失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 修改密码 (Change Password)
  const changePassword = async (passwordData: PasswordChangeRequest): Promise<void> => {
    try {
      loading.value = true;
      error.value = null;

      await changePasswordApi(passwordData);
      
      ElMessage.success('密码修改成功，请重新登录');
      
      // 密码修改后需要重新登录
      await logout();
    } catch (err: any) {
      error.value = err.message || '密码修改失败';
      ElMessage.error(error.value);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 更新用户信息 (Update User Info)
  const updateUserInfo = (newUserInfo: Partial<UserInfo>): void => {
    if (user.value) {
      user.value = { ...user.value, ...newUserInfo };
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(user.value));
    }
  };

  // 检查是否需要刷新Token (Check if Token Needs Refresh)
  const checkTokenExpiry = async (): Promise<void> => {
    if (!token.value || !refreshToken.value) {
      return;
    }

    try {
      const isValid = await validateTokenApi(token.value);
      if (!isValid) {
        await refreshTokenAction(refreshToken.value);
      }
    } catch (error) {
      console.error('Token检查失败:', error);
      clearAuthData();
    }
  };

  return {
    // 状态
    isAuthenticated,
    user,
    token,
    refreshToken,
    loading,
    error,
    
    // 计算属性
    permissions,
    userRole,
    isAdmin,
    userName,
    userAvatar,
    
    // 方法
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    initializeAuth,
    login,
    logout,
    refreshTokenAction,
    fetchUserInfo,
    changePassword,
    updateUserInfo,
    checkTokenExpiry,
    clearAuthData
  };
});
